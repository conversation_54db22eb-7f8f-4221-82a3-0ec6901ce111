[{"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5376e Safari/8536.25", "browser": {"name": "Mobile Safari", "version": "6.0", "major": "6"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "536.26"}, "os": {"name": "iOS", "version": "6.0"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5376e Safari/8536.25", "browser": {"name": "Mobile Safari", "version": "6.0", "major": "6"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "536.26"}, "os": {"name": "iOS", "version": "6.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.1.30"}, "os": {"name": "iOS", "version": "10.3.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 10_3_3 like Mac OS X) AppleWebKit/603.3.8 (KHTML, like Gecko) Version/10.0 Mobile/14G60 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.3.8"}, "os": {"name": "iOS", "version": "10.3.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5355d Safari/8536.25", "browser": {"name": "Mobile Safari", "version": "6.0", "major": "6"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "536.26"}, "os": {"name": "iOS", "version": "6.0"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 5_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko ) Version/5.1 Mobile/9B176 Safari/7534.48.3", "browser": {"name": "Mobile Safari", "version": "5.1", "major": "5"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "534.46"}, "os": {"name": "iOS", "version": "5.1"}}, {"ua": "Mozilla/5.0 (iPod; U; CPU iPhone OS 4_3_3 like Mac OS X; ja-jp) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8J2 Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPod", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.3.3"}}, {"ua": "Mozilla/5.0 (iPod; U; CPU iPhone OS 4_3_1 like Mac OS X; zh-cn) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8G4 Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPod", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.3.1"}}, {"ua": "Mozilla/5.0 (iPod; U; CPU iPhone OS 4_2_1 like Mac OS X; he-il) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8C148 Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPod", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.2.1"}}, {"ua": "Mozilla/5.0 (iPhone; U; ru; CPU iPhone OS 4_2_1 like Mac OS X; ru) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8C148a Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.2.1"}}, {"ua": "Mozilla/5.0 (iPhone; U; ru; CPU iPhone OS 4_2_1 like Mac OS X; fr) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8C148a Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.2.1"}}, {"ua": "Mozilla/5.0 (iPhone; U; fr; CPU iPhone OS 4_2_1 like Mac OS X; fr) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8C148a Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.2.1"}}, {"ua": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_3_1 like Mac OS X; zh-tw) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8G4 Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.3.1"}}, {"ua": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_3 like Mac OS X; pl-pl) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.3"}}, {"ua": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_3 like Mac OS X; fr-fr) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.3"}}, {"ua": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_3 like Mac OS X; en-gb) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.3"}}, {"ua": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_2_1 like Mac OS X; ru-ru) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8C148 Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.2.1"}}, {"ua": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_2_1 like Mac OS X; nb-no) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8C148a Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.2.1"}}, {"ua": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_1 like Mac OS X; en-us) AppleWebKit/532.9 (KHTML, like Gecko) Version/4.0.5 Mobile/8B5097d Safari/6531.22.7", "browser": {"name": "Mobile Safari", "version": "4.0.5", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "532.9"}, "os": {"name": "iOS", "version": "4.1"}}, {"ua": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_1 like Mac OS X; en-us) AppleWebKit/532.9 (KHTML, like Gecko) Version/4.0.5 Mobile/8B117 Safari/6531.22.7", "browser": {"name": "Mobile Safari", "version": "4.0.5", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "532.9"}, "os": {"name": "iOS", "version": "4.1"}}, {"ua": "Mozilla/5.0(iPad; U; CPU iPhone OS 3_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B314 Safari/531.21.10gin_lib.cc", "browser": {"name": "Mobile Safari", "version": "4.0.4", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "531.21.10"}, "os": {"name": "iOS", "version": "3.2"}}, {"ua": "Mozilla/5.0(iPad; U; CPU iPhone OS 3_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B314 Safari/531.21.10", "browser": {"name": "Mobile Safari", "version": "4.0.4", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "531.21.10"}, "os": {"name": "iOS", "version": "3.2"}}, {"ua": "Mozilla/5.0(iPad; U; CPU iPhone OS 3_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B314 Safari/123", "browser": {"name": "Mobile Safari", "version": "4.0.4", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "531.21.10"}, "os": {"name": "iOS", "version": "3.2"}}, {"ua": "Mozilla/5.0 (iPhone; U; CPU OS 3_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B334b Safari/531.21.10", "browser": {"name": "Mobile Safari", "version": "4.0.4", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "531.21.10"}, "os": {"name": "iOS", "version": "3.2"}}, {"ua": "Mozilla/5.0 (iPhone Simulator; U; CPU iPhone OS 3_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7D11 Safari/531.21.10", "browser": {"name": "Mobile Safari", "version": "4.0.4", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone Simulator", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "531.21.10"}, "os": {"name": "iOS", "version": "3.2"}}, {"ua": "Mozilla/5.0 (iPad;U;CPU OS 3_2_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B500 Safari/531.21.10", "browser": {"name": "Mobile Safari", "version": "4.0.4", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "531.21.10"}, "os": {"name": "iOS", "version": "3.2.2"}}, {"ua": "Mozilla/5.0 (iPad; U; CPU OS 3_2_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B500 Safari/53", "browser": {"name": "Mobile Safari", "version": "4.0.4", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "531.21.10"}, "os": {"name": "iOS", "version": "3.2.2"}}, {"ua": "Mozilla/5.0 (iPad; U; CPU OS 3_2 like Mac OS X; es-es) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B367 Safari/531.21.10", "browser": {"name": "Mobile Safari", "version": "4.0.4", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "531.21.10"}, "os": {"name": "iOS", "version": "3.2"}}, {"ua": "Mozilla/5.0 (iPad; U; CPU OS 3_2 like Mac OS X; es-es) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B360 Safari/531.21.10", "browser": {"name": "Mobile Safari", "version": "4.0.4", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "531.21.10"}, "os": {"name": "iOS", "version": "3.2"}}, {"ua": "Mozilla/5.0 (Mozilla/5.0 (iPhone; U; CPU iPhone OS 2_0_1 like Mac OS X; fr-fr) AppleWebKit/525.18.1 (KHTML, like Gecko) Version/3.1.1 Mobile/5G77 Safari/525.20", "browser": {"name": "Mobile Safari", "version": "3.1.1", "major": "3"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "525.18.1"}, "os": {"name": "iOS", "version": "2.0.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 5_0 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9A334 Safari/7534.48.3", "browser": {"name": "Mobile Safari", "version": "5.1", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "534.46"}, "os": {"name": "iOS", "version": "5.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.4 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.4", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_1_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.1", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.1.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.2", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.4.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.3.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.4.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.5 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.5", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.3.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 9_3_5 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13G36 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.3.5"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.2", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.4"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_3 like Mac OS X) AppleWebKit/603.3.8 (KHTML, like Gecko) Version/10.0 Mobile/14G60 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.3.8"}, "os": {"name": "iOS", "version": "10.3.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.0.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.1", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.1.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5355d Safari/8536.25", "browser": {"name": "Mobile Safari", "version": "6.0", "major": "6"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "536.26"}, "os": {"name": "iOS", "version": "6.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.4"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_2_6 like Mac OS X) AppleWebKit/604.5.6 (KHTML, like Gecko) Version/11.0 Mobile/15D100 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.5.6"}, "os": {"name": "iOS", "version": "11.2.6"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_2_1 like Mac OS X) AppleWebKit/602.4.6 (KHTML, like Gecko) Version/10.0 Mobile/14D27 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "602.4.6"}, "os": {"name": "iOS", "version": "10.2.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.3", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.2.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.0"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 10_3_3 like Mac OS X) AppleWebKit/603.3.8 (KHTML, like Gecko) Version/10.0 Mobile/14G60 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.3.8"}, "os": {"name": "iOS", "version": "10.3.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_2 like Mac OS X) AppleWebKit/603.2.4 (KHTML, like Gecko) Version/10.0 Mobile/14F89 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.2.4"}, "os": {"name": "iOS", "version": "10.3.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13B143 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_2 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Version/7.0 Mobile/11D257 Safari/9537.53", "browser": {"name": "Mobile Safari", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "537.51.2"}, "os": {"name": "iOS", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 9_3_2 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13F69 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.3.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.2", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.4.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_2_1 like Mac OS X) AppleWebKit/604.4.7 (KHTML, like Gecko) Version/11.0 Mobile/15C153 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.4.7"}, "os": {"name": "iOS", "version": "11.2.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.1", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.3.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_1_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1.4"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_3_2 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13F69 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.3.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_2 like Mac OS X) AppleWebKit/602.3.12 (KHTML, like Gecko) Version/10.0 Mobile/14C92 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "602.3.12"}, "os": {"name": "iOS", "version": "10.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 10_2_1 like Mac OS X) AppleWebKit/602.4.6 (KHTML, like Gecko) Version/10.0 Mobile/14D27 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "602.4.6"}, "os": {"name": "iOS", "version": "10.2.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_2_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13D15 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.2.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.1.30"}, "os": {"name": "iOS", "version": "10.3.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_2_5 like Mac OS X) AppleWebKit/604.5.6 (KHTML, like Gecko) Version/11.0 Mobile/15D60 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.5.6"}, "os": {"name": "iOS", "version": "11.2.5"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_0_2 like Mac OS X) AppleWebKit/602.1.50 (KHTML, like Gecko) Version/10.0 Mobile/14A456 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "602.1.50"}, "os": {"name": "iOS", "version": "10.0.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12F70 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_3_5 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13G36 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.3.5"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 8_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12F69 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_1_1 like Mac OS X) AppleWebKit/602.2.14 (KHTML, like Gecko) Version/10.0 Mobile/14B100 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "602.2.14"}, "os": {"name": "iOS", "version": "10.1.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.4.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_1 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12B411 Safari/600.1.4 (compatible; YandexMobileBot/3.0; +http://yandex.com/bots)", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_2_2 like Mac OS X) AppleWebKit/604.4.7 (KHTML, like Gecko) Version/11.0 Mobile/15C202 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.4.7"}, "os": {"name": "iOS", "version": "11.2.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_4_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.2", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.4.4"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_1_2 like Mac OS X) AppleWebKit/604.3.5 (KHTML, like Gecko) Version/11.0 Mobile/15B202 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.3.5"}, "os": {"name": "iOS", "version": "11.1.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 9_2_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13D15 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.2.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_3_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13E238 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.3.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 7_1_2 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Version/7.0 Mobile/11D257 Safari/9537.53", "browser": {"name": "Mobile Safari", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "537.51.2"}, "os": {"name": "iOS", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.38"}, "os": {"name": "iOS", "version": "11.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.3", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_0_3 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A432 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.38"}, "os": {"name": "iOS", "version": "11.0.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.3.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 5_1_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9B206 Safari/7534.48.3", "browser": {"name": "Mobile Safari", "version": "5.1", "major": "5"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "534.46"}, "os": {"name": "iOS", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_2 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13C75 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_4_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.2", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.4.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_4_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.2", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.4.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 8_1_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12B440 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.1.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 9_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13B143 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 8_1_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12B466 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.1.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 9_2 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13C75 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 8_4 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12H143 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.4"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_4_1 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12H321 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.4.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.0"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 9_3_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13E238 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.3.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_3 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13E188a Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_4 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12H143 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.4"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.2", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.4"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 10_3_2 like Mac OS X) AppleWebKit/603.2.4 (KHTML, like Gecko) Version/10.0 Mobile/14F89 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.2.4"}, "os": {"name": "iOS", "version": "10.3.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 10_0_2 like Mac OS X) AppleWebKit/602.1.50 (KHTML, like Gecko) Version/10.0 Mobile/14A456 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "602.1.50"}, "os": {"name": "iOS", "version": "10.0.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_1_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12B440 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.1.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_0 like Mac OS X) AppleWebKit/602.1.38 (KHTML, like Gecko) Version/10.0 Mobile/14A300 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "602.1.38"}, "os": {"name": "iOS", "version": "10.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_1_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12B466 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.1.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 10_3_1 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.1.30"}, "os": {"name": "iOS", "version": "10.3.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 8_4_1 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12H321 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.4.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 10_1_1 like Mac OS X) AppleWebKit/602.2.14 (KHTML, like Gecko) Version/10.0 Mobile/14B100 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "602.2.14"}, "os": {"name": "iOS", "version": "10.1.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 10_2 like Mac OS X) AppleWebKit/602.3.12 (KHTML, like Gecko) Version/10.0 Mobile/14C92 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "602.3.12"}, "os": {"name": "iOS", "version": "10.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.3", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.2.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.0.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 8_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12D508 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_2_6 like Mac OS X) AppleWebKit/604.5.6 (KHTML, like Gecko) Version/11.0 Mobile/15D100 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.5.6"}, "os": {"name": "iOS", "version": "11.2.6"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_3_4 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13G35 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.3.4"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.4"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 8_1 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12B410 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_3_3 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13G34 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.3.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_1 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12B410 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12D508 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.0"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 7_1_1 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Version/7.0 Mobile/11D201 Safari/9537.53", "browser": {"name": "Mobile Safari", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "537.51.2"}, "os": {"name": "iOS", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.1", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_2_1 like Mac OS X) AppleWebKit/604.4.7 (KHTML, like Gecko) Version/11.0 Mobile/15C153 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.4.7"}, "os": {"name": "iOS", "version": "11.2.1"}}, {"ua": "\"Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.38"}, "os": {"name": "iOS", "version": "11.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13B143 Safari/601.1 (compatible; AdsBot-Google-Mobile; +http://www.google.com/mobile/adsbot.html)", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_4_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.2", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.4.4"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_1_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1.4"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 7_0_4 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/7.0 Mobile/11B554a Safari/9537.53", "browser": {"name": "Mobile Safari", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "537.51.1"}, "os": {"name": "iOS", "version": "7.0.4"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_0_3 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A432 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.38"}, "os": {"name": "iOS", "version": "11.0.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 8_1_1 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12B435 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.1.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B329 Safari/8536.25", "browser": {"name": "Mobile Safari", "version": "6.0", "major": "6"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "536.26"}, "os": {"name": "iOS", "version": "6.1.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_4_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.2", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.4.5"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_2_5 like Mac OS X) AppleWebKit/604.5.6 (KHTML, like Gecko) Version/11.0 Mobile/15D60 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.5.6"}, "os": {"name": "iOS", "version": "11.2.5"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_1_1 like Mac OS X) AppleWebKit/604.3.5 (KHTML, like Gecko) Version/11.0 Mobile/15B150 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.3.5"}, "os": {"name": "iOS", "version": "11.1.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 9_3_4 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13G35 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.3.4"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_4_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.2", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.4.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_1 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Version/7.0 Mobile/11D201 Safari/9537.53", "browser": {"name": "Mobile Safari", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "537.51.2"}, "os": {"name": "iOS", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_0_2 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13A452 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.0.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_0_1 like Mac OS X) AppleWebKit/602.1.50 (KHTML, like Gecko) Version/10.0 Mobile/14A403 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "602.1.50"}, "os": {"name": "iOS", "version": "10.0.1"}}, {"ua": "mozilla/5.0 (ipad; cpu os 12_4_3 like mac os x) applewebkit/605.1.15 (khtml, like gecko) version/12.1.2 mobile/15e148 safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.2", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "ipad", "vendor": "Apple"}, "engine": {"name": "webkit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.4.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_2 like Mac OS X) AppleWebKit/604.4.7 (KHTML, like Gecko) Version/11.0 Mobile/15C114 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.4.7"}, "os": {"name": "iOS", "version": "11.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.1", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.1.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_0_1 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A402 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.38"}, "os": {"name": "iOS", "version": "11.0.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_1 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12B411 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 9_0_2 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13A452 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.0.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 8_0_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12A405 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.0.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_0_2 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A421 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.38"}, "os": {"name": "iOS", "version": "11.0.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 7_0_4 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/7.0 Mobile/11B554a Safari/9537.53", "browser": {"name": "Mobile Safari", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "537.51.1"}, "os": {"name": "iOS", "version": "7.0.4"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_1 like Mac OS X) AppleWebKit/604.3.5 (KHTML, like Gecko) Version/11.0 Mobile/15B93 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.3.5"}, "os": {"name": "iOS", "version": "11.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 7_1 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Version/7.0 Mobile/11D167 Safari/9537.53", "browser": {"name": "Mobile Safari", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "537.51.2"}, "os": {"name": "iOS", "version": "7.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_1_2 like Mac OS X) AppleWebKit/604.3.5 (KHTML, like Gecko) Version/11.0 Mobile/15B202 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.3.5"}, "os": {"name": "iOS", "version": "11.1.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 9_3_3 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13G34 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.3.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 5_0_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9A405 Safari/7534.48.3", "browser": {"name": "Mobile Safari", "version": "5.1", "major": "5"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "534.46"}, "os": {"name": "iOS", "version": "5.0.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_2_2 like Mac OS X) AppleWebKit/604.4.7 (KHTML, like Gecko) Version/11.0 Mobile/15C202 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.4.7"}, "os": {"name": "iOS", "version": "11.2.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.4 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.4", "major": "13"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_0 like Mac OS X) AppleWebKit/600.1.3 (KHTML, like Gecko) Version/8.0 Mobile/12A4345d Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.3"}, "os": {"name": "iOS", "version": "8.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_0_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12A405 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.0.2"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A523 Safari/8536.25", "browser": {"name": "Mobile Safari", "version": "6.0", "major": "6"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "536.26"}, "os": {"name": "iOS", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.1", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B329 Safari/8536.25", "browser": {"name": "Mobile Safari", "version": "6.0", "major": "6"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "536.26"}, "os": {"name": "iOS", "version": "6.1.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_1_1 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12B435 Safari/600.1.4", "browser": {"name": "Mobile Safari", "version": "8.0", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "600.1.4"}, "os": {"name": "iOS", "version": "8.1.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 7_0_6 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/7.0 Mobile/11B651 Safari/9537.53", "browser": {"name": "Mobile Safari", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "537.51.1"}, "os": {"name": "iOS", "version": "7.0.6"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 7_1 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Version/7.0 Mobile/11D167 Safari/9537.53", "browser": {"name": "Mobile Safari", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "537.51.2"}, "os": {"name": "iOS", "version": "7.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/10.3 Mobile/14E277 Safari/603.1.30", "browser": {"name": "Mobile Safari", "version": "10.3", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.1.30"}, "os": {"name": "iOS", "version": "10.3"}}, {"ua": "Mozilla/5.0 (iPad; U; CPU OS 4_3_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8J2 Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.3.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 5_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9B176 Safari/7534.48.3", "browser": {"name": "Mobile Safari", "version": "5.1", "major": "5"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "534.46"}, "os": {"name": "iOS", "version": "5.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_3 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13E233 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_0_1 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A402 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.38"}, "os": {"name": "iOS", "version": "11.0.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.5 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.5", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.3.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 10_3_3 like Mac OS X) AppleWebKit/603.3.8 (KHTML, like Gecko) Version/10.0 Mobile/14G60 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.3.8"}, "os": {"name": "iOS", "version": "10.3.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.1.1", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.5.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.1.2", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.6.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.1.1", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.5.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_1_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5376e Safari/8536.25", "browser": {"name": "Mobile Safari", "version": "6.0", "major": "6"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "536.26"}, "os": {"name": "iOS", "version": "6.0"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 12_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.1.2", "major": "12"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "14.0", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.1.1", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.5.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_1_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1", "browser": {"name": "Mobile Safari", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.1.30"}, "os": {"name": "iOS", "version": "10.3.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 7_0 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/7.0 Mobile/11A465 Safari/9537.53 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)", "browser": {"name": "Mobile Safari", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "537.51.1"}, "os": {"name": "iOS", "version": "7.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU OS 10_14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/14E304 Safari/605.1.15", "browser": {"name": "Mobile Safari", "version": "12.1.1", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "10.14.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5376e Safari/8536.25", "browser": {"name": "Mobile Safari", "version": "6.0", "major": "6"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "536.26"}, "os": {"name": "iOS", "version": "6.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "14.0", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.5"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 14_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "14.0", "major": "14"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.5"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "14.1", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.5"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_0_1 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A402 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.38"}, "os": {"name": "iOS", "version": "11.0.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "15.0", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "15.0.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5376e Safari/8536.25", "browser": {"name": "Mobile Safari", "version": "6.0", "major": "6"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "536.26"}, "os": {"name": "iOS", "version": "6.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "14.1.1", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.6"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/12.0.0 Mobile/15A5370a Safari/602.1", "browser": {"name": "Mobile Safari", "version": "12.0.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.1.30"}, "os": {"name": "iOS", "version": "14.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "15.4", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "15.4"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "15.2", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "15.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5376e Safari/8536.25", "browser": {"name": "Mobile Safari", "version": "6.0", "major": "6"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "536.26"}, "os": {"name": "iOS", "version": "6.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_0_1 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A402 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.38"}, "os": {"name": "iOS", "version": "11.0.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_0_1 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A402 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "604.1.38"}, "os": {"name": "iOS", "version": "11.0.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 16_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "16.3", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.3.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "16.6", "major": "16"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.6"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "17.5", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.5"}}, {"ua": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_3_2 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8H7 Safari/6533.18.5", "browser": {"name": "Mobile Safari", "version": "5.0.2", "major": "5"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "533.17.9"}, "os": {"name": "iOS", "version": "4.3.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_1_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "17.4.1", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.4.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 16_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "16.3", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.3.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13B143 Safari/601.1 (compatible; Baiduspider-render/2.0; +http://www.baidu.com/search/spider.html)", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_1_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "17.5", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.5.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "15.6", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "15.6"}}, {"ua": "Mozilla/5.0 (iPhone; CPU OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/14E304 Safari/605.1.15", "browser": {"name": "Mobile Safari", "version": "17.4.1", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.5.1"}}, {"ua": "Mozilla/5.0 (iPhone/ CPUmns iPhone OS 17_3_2 like Mac OS X) AppleWebKit/605.1.14 (KHTML, like Gecko) Version/17.1.2 Mobile/15lkh148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "17.1.2", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.14"}, "os": {"name": "iOS", "version": "17.3.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/15.0.0 Mobile/15A5370a Safari/602.1", "browser": {"name": "Mobile Safari", "version": "15.0.0", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "603.1.30"}, "os": {"name": "iOS", "version": "15.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "15.4", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.4"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 16_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "16.3", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.3.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 16 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5.17 Mobile/N1WEFQ Safari/617.32.2", "browser": {"name": "Mobile Safari", "version": "16.5.17", "major": "16"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16"}}, {"ua": "Mozilla/5.082584686 Mozilla/5.0 (iPhone; CPU iPhone OS 11_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.4.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "16.5", "major": "16"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.5"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.2 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.2", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "17.6", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.6.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "17.5", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.5.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_7_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "16.6", "major": "16"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.7.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.4.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 9_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13B143 Safari/601.1", "browser": {"name": "Mobile Safari", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "601.1.46"}, "os": {"name": "iOS", "version": "9.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "18.0", "major": "18"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "18.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2 like Mac OS X) AppleWebKit/623.2 (KHTML, like Gecko) Version/14.6 Mobile/1PB4I5 Safari/623.2", "browser": {"name": "Mobile Safari", "version": "14.6", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "623.2"}, "os": {"name": "iOS", "version": "15.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_1_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.1.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "15.6", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "15.6"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 16_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "16.3", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.3.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.3", "major": "13"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.2.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "16.6", "major": "16"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.6.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/14E304 Safari/605.1.15", "browser": {"name": "Mobile Safari", "version": "17.4.1", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.5.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "16.6", "major": "16"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.6"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "12.0", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "12.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "14.0", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "14.0.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.26 Mobile/Y650R1 Safari/619.10", "browser": {"name": "Mobile Safari", "version": "17.4.26", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5376e Safari/8536.25", "browser": {"name": "Mobile Safari", "version": "6.0", "major": "6"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "536.26"}, "os": {"name": "iOS", "version": "6.0"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "18.1.1", "major": "18"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "18.1.1"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 16_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "16.3", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.3.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "18.1", "major": "18"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "18.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1.15 Mobile/GCUL1T Safari/618.5.14", "browser": {"name": "Mobile Safari", "version": "16.1.15", "major": "16"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.5.5"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "18.1", "major": "18"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "18.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "16.6", "major": "16"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.6"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "17.6", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.6.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "13.0.3", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "13.2.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPad; CPU OS 16_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "16.3", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "iPad", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "16.3.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "18.4", "major": "18"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "18.4.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "11.0", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "11.3"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "18.3.1", "major": "18"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "18.3.2"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "17.5", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.5.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "17.6.1", "major": "17"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "17.6.1"}}, {"ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1", "browser": {"name": "Mobile Safari", "version": "18.1.1", "major": "18"}, "cpu": {}, "device": {"type": "mobile", "model": "iPhone", "vendor": "Apple"}, "engine": {"name": "WebKit", "version": "605.1.15"}, "os": {"name": "iOS", "version": "18.1.1"}}]