[{"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/540.0 (KHTML, like Gecko) Ubuntu/10.10 Chrome/9.1.0.0 Safari/540.0", "browser": {"name": "Chrome", "version": "9.1.0.0", "major": "9"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "540.0"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/540.0 (KHTML, like Gecko) Ubuntu/10.10 Chrome/8.1.0.0 Safari/540.0", "browser": {"name": "Chrome", "version": "8.1.0.0", "major": "8"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "540.0"}, "os": {"name": "Ubuntu", "version": "10.10"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.11 (KHTML, like Gecko) Ubuntu/14.04.6 Chrome/81.0.3990.0 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.3990.0", "major": "81"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.11"}, "os": {"name": "Ubuntu", "version": "14.04.6"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.11 (KHTML, like Gecko) Ubuntu/14.04.6 Chrome/81.0.3990.0 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.3990.0", "major": "81"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.11"}, "os": {"name": "Ubuntu", "version": "14.04.6"}}, {"ua": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6299.209 Safari/537.36", "browser": {"name": "Chrome", "version": "122.0.6299.209", "major": "122"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "122.0.6299.209"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (Wayland like X11; Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.6302.197 Safari/537.36", "browser": {"name": "Chrome", "version": "124.0.6302.197", "major": "124"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "124.0.6302.197"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.11 (KHTML, like Gecko) Ubuntu/14.04.6 Chrome/81.0.3990.0 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.3990.0", "major": "81"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.11"}, "os": {"name": "Ubuntu", "version": "14.04.6"}}, {"ua": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6303.215 Safari/537.36", "browser": {"name": "Chrome", "version": "123.0.6303.215", "major": "123"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "123.0.6303.215"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6787.80 Safari/537.36", "browser": {"name": "Chrome", "version": "132.0.6787.80", "major": "132"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "132.0.6787.80"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.11 (KHTML, like Gecko) Ubuntu/14.04.6 Chrome/81.0.3990.0 Safari/537.36", "browser": {"name": "Chrome", "version": "81.0.3990.0", "major": "81"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "535.11"}, "os": {"name": "Ubuntu", "version": "14.04.6"}}]