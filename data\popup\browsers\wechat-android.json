[{"ua": "Mozilla/5.0 (Linux; Android 13; 22127RK46C Build/TKQ1.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/107.0.5304.141 Mobile Safari/537.36 XWEB/5127 MMWEBSDK/20230604 MMWEBID/7189 MicroMessenger/8.0.38.2400(0x28002639) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 qcloudcdn-xinan Request-Source=4 Request-Channel=99", "browser": {"name": "WeChat", "version": "8.0.38.2400", "major": "8"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "22127RK46C", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "107.0.5304.141"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; STG-AL00 Build/HUAWEISTG-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/107.0.5304.141 Mobile Safari/537.36 XWEB/5127 MMWEBSDK/20230504 MMWEBID/4186 MicroMessenger/8.0.37.2380(0x2800255B) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 qcloudcdn-xinan Request-Source=4 Request-Channel=99", "browser": {"name": "WeChat", "version": "8.0.37.2380", "major": "8"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "STG-AL00", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "107.0.5304.141"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 13; M2007J1SC Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/107.0.5304.141 Mobile Safari/537.36 XWEB/5127 MMWEBSDK/20230405 MMWEBID/1151 MicroMessenger/8.0.35.2360(0x2800235D) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 qcloudcdn-xinan Request-Source=4 Request-Channel=99", "browser": {"name": "WeChat", "version": "8.0.35.2360", "major": "8"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "M2007J1SC", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "107.0.5304.141"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Mobile Safari/537.36 MicroMessenger/7.0.1", "browser": {"name": "WeChat", "version": "7.0.1", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 5", "vendor": "LG"}, "engine": {"name": "Blink", "version": "99.0.4844.51"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 13; 22127RK46C Build/TKQ1.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/107.0.5304.141 Mobile Safari/537.36 XWEB/5127 MMWEBSDK/20230604 MMWEBID/7189 MicroMessenger/8.0.38.2400(0x28002639) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 qcloudcdn-xinan Request-Source=4 Request-Channel", "browser": {"name": "WeChat", "version": "8.0.38.2400", "major": "8"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "22127RK46C", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "107.0.5304.141"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0; LON-AL00 Build/HUAWEILON-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.132 MQQBrowser/6.2 TBS/044279 Mobile Safari/537.36 MicroMessenger/6.7.2.1340(0x26070235) NetType/WIFI Language/zh_CN", "browser": {"name": "WeChat", "version": "6.7.2.1340", "major": "6"}, "cpu": {}, "device": {"type": "mobile", "model": "LON-AL00", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "57.0.2987.132"}, "os": {"name": "Android", "version": "8.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 13; 22127RK46C Build/TKQ1.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/107.0.5304.141 Mobile Safari/537.36 XWEB/5127 MMWEBSDK/20230604 MMWEBID/7189 MicroMessenger/8.0.38.2400(0x28002639) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 qcloudcdn-xinan Request-Source=4 Request-Channel", "browser": {"name": "WeChat", "version": "8.0.38.2400", "major": "8"}, "cpu": {"architecture": "arm64"}, "device": {"type": "mobile", "model": "22127RK46C", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "107.0.5304.141"}, "os": {"name": "Android", "version": "13"}}]