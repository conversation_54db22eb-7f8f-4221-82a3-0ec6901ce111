{"extensionName": {"message": "User-Agent Switcher and Manager", "description": "Name of the extension."}, "extensionDescription": {"message": "Подменяет User-Agent на веб-сайтах, пытающихся собрать информацию о ваших путешествиях по сети и выдать не нужное вам содержимое", "description": "Description of the extension."}, "userAgentSwitcherandManagerOptions": {"message": "User-Agent Switcher and Manager :: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "blackListMode": {"message": "Режим черного списка"}, "description": {"message": "Описание"}, "blackListModeDescription": {"message": "Применить установленную строку User-Agent ко всем вкладкам за исключением содержащих следующие домены верхнего уровня (список доменов разделяется запятыми). Важно: даже если применяемая к окну строка User-Agent установлена из всплывающего окна, для этого списка будет использоваться строка User-Agent вашего браузера по умолчанию."}, "whiteListMode": {"message": "Режим белого списка"}, "whiteListModeDescription": {"message": "Применять установленную строку User-Agent только к вкладкам, содержащим следующие домены верхнего уровня. Важно: даже если применяемая к окну строка User-Agent установлена из всплывающего окна, данный User-Agent заменит собой глобальный."}, "customMode": {"message": "Режим настройки"}, "customModeDescription": {"message": "Пробовать считывание строки User-Agent из объекта JSON; в другом случае использовать строку User-Agent по умолчанию или установленную во всплывающем окне. Используйте \"*\" в качестве сервера для применения ко всем доменам. Вы можете случайно выбирать из нескольких строк User-Agent с помощью массива вместо фиксированной строки. Если в объекте JSON присутствует ключ \"_\", отсылающий к массиву серверов, то расширение будет случайно выбирать строку User-Agent для каждого сервера в списке. Это полезно, если вы не желаете случайно менять User-Agent до перезапуска браузера."}, "insertSample": {"message": "Вставить пример"}, "cache": {"message": "Использовать кэширование для улучшения производительности (рекомендуемое значение true). Отключите эту опцию только при использовании режима настройки, когда нужно изменять строку User-Agent с помощью списка при каждом запросе."}, "exactMatch": {"message": "Использовать точное совпадение (если включено, вам нужно добавлять все поддомены в черный и белый списки как задумано. Если выключено, все поддомены проходят проверку на совпадение (например, www.google.com проходит проверку, если google.com находится в списке))"}, "faqs": {"message": "Открывать страницу FAQ при обновлении"}, "log": {"message": "Отобр<PERSON><PERSON><PERSON><PERSON>ь отладочную информацию в консоли браузера"}, "disableSpoofing": {"message": "Отключить подмену User-Agent"}, "disableSpoofingDescription": {"message": "Разделяемый запятыми список ключевых слов, на которых расширение не должно подменять заголовок User-Agent. Используйте этот список для защиты URL, содержащих эти ключевые слова. Каждое ключевое слово должно быть длиной не менее 5 символов."}, "customUserAgentParsing": {"message": "Парсинг установленного User-Agent"}, "customUserAgentParsingDescription": {"message": "Объект JSON для обхода внутреннего метода парсинга строки User-Agent. Ключи — настоящие строки User-Agent. Значение каждого ключа- объект ключей, требуемых для установки объекта \"navigator\". Вы можете использовать ключевое слово \"[delete]\", если вам нужен ключ в объекте \"navigator\" для удаления."}, "siblingHostnames": {"message": "Родственные домены"}, "siblingHostnamesDescription": {"message": "Массив JSON, содержащий одну или больше групп серверов с отдельной строкой User-Agent на группу. Для всех серверов в одной группе вычисление строки User-Agent проводится только один раз, все остальные члены группы используют ту же самую строку. С помощью этого можно убедиться, что група связанных веб-сайтов имеет доступ только к одной и той же строке User-Agent."}, "importSettings": {"message": "Импортировать настройки"}, "exportSettings": {"message": "Экспортировать настройки"}, "exportSettingsTitle": {"message": "Чтобы сгенерировать уменьшенную версию, нажмите эту кнопку, удерживая клавишу Shift"}, "help": {"message": "Страница FAQ (Помощь)"}, "donate": {"message": "Поддержать разработку"}, "save": {"message": "Сохранить"}, "managedStorage": {"message": "Данное расширение поддерживает Managed Storage, и эти настройки могут быть изменены автоматически или установлены изначально администратором домена. Прочтите FAQ для получения более подробной информации."}, "options": {"message": "Настройки"}, "optionsTitle": {"message": "Открыть страницу настроек"}, "restart": {"message": "Перезапустить"}, "restartTitle": {"message": "Нажмите, чтобы перезапустить расширение. Это очистит все строки User-Agent, примененные к окну."}, "refreshTab": {"message": "Обновить вкладку"}, "refreshTabTitle": {"message": "Обновить текущую страницу"}, "reset": {"message": "Сбросить"}, "resetTitle": {"message": "Сбросить строку User-Agent браузера на значение по умолчанию. Это не сбросит строки User-Agent, примененные к окну. Чтобы сбросить их, нажмите кнопку 'Перезапустить'."}, "testUA": {"message": "Тест UA"}, "testUATitle": {"message": "Тестировать вашу строку User-Agent"}, "considerContainers": {"message": "Учитывать контейнеры"}, "considerContainersTitle": {"message": "Позволять расширению иметь доступ к контейнерам вашего браузера. При наличии доступа вкладки внутри изолированных контейнеров не используют строку User-Agent контейнера по умолчанию. Вам нужно устанавливать эту строку для каждого нового контейнера."}, "applyActiveWindow": {"message": "Применить (активное окно)"}, "applyActiveWindowTitle": {"message": "Установить эту строку User-Agent для всех вкладок внутри текущего окна"}, "applyAllWindows": {"message": "Применить (все окна)"}, "applyAllWindowsTitle": {"message": "Установить эту строку User-Agent в качестве основной строки браузера"}, "applyContainer": {"message": "Применить (контейнер)"}, "applyContainerTitle": {"message": "Установить эту строку User-Agent в качестве строки текущего контейнера"}, "applyContainerWindow": {"message": "Применить (контейнер в окне)"}, "applyContainerWindowTitle": {"message": "Установить эту строку User-Agent для всех вкладок внутри текущего окна контейнера"}, "resetContainer": {"message": "Сбросить (контейнер)"}, "resetContainerTitle": {"message": "Сбросить строку User-Agent контейнера на значение по умолчанию. Это не сбросит строки User-Agent, примененные к окну. Чтобы сбросить их, нажмите кнопку 'Перезапустить'."}, "oscpuTitle": {"message": "Это поле только для чтения. Используйте страницу настроек для парсинга вручную."}, "appVersionTitle": {"message": "Это поле только для чтения. Используйте страницу настроек для парсинга вручную."}, "platformTitle": {"message": "Это поле только для чтения. Используйте страницу настроек для парсинга вручную."}, "vendorTitle": {"message": "Это поле только для чтения. Используйте страницу настроек для парсинга вручную."}, "productTitle": {"message": "Это поле только для чтения. Используйте страницу настроек для парсинга вручную."}, "uaTitle": {"message": "Чтобы установить пустую строку User-Agent, используйте ключевое слово 'empty'. Чтобы составить собственную строку User-Agent, основанную на текущем объекте navigator браузера, используйте нотацию ${}. Все внутри этой нотации будет прочитано из объекта 'navigator'. На<PERSON>ример, чтобы присоединить строку к User-Agent по умолчанию, используйте '${userAgent} ПРИСОЕДИНЕННАЯ СТРОКА'"}, "uaPlaceholder": {"message": "Ваша предпочитаемая строка User-Agent"}, "noMatch": {"message": "Для данного запроса нет строк User-Agent"}, "ztoa": {"message": "От Z до A"}, "atoz": {"message": "От A до Z"}, "filterAmong": {"message": "Выбрать из $1"}, "filterAgents": {"message": "Выбрать UA"}, "msgDefaultUA": {"message": "User-Agent по умолчанию, вместо этого нажмите кнопку 'Сбросить'"}, "msgUASet": {"message": "User-Agent установлен"}, "msgDisabledOnContainer": {"message": "Отключено в этом контейнере. Используется строка User-Agent по умолчанию."}, "msgDisabled": {"message": "Отключено. Используется строка User-Agent по умолчанию."}, "optionsSaved": {"message": "Настройки сохранены."}, "dbReset": {"message": "Щелкните 2 раза левой кнопкой мыши для сброса!"}}