{"browser": ["Bot", "IE", "<PERSON><PERSON><PERSON><PERSON>", "Opera", "Firefox", "Chrome", "Mobile Chrome", "Mobile Firefox", "Mobile Safari", "IEMobile", "Safari", "Android Browser", "Opera Mobi", "Opera Mini", "UC<PERSON><PERSON><PERSON>", "Puffin", "Samsung Internet", "Yandex", "MIUI Browser", "Edge", "WebKit", "Blazer", "BOLT", "Fennec", "<PERSON><PERSON>", "Minimo", "Kindle", "Skyfire", "Chromium", "Avant", "Maxthon", "Arora", "Mozilla", "Epiphany", "Camino", "Chimera", "Comodo Dragon", "<PERSON><PERSON><PERSON>", "Links", "Firebird", "Swiftfox", "Netscape", "Flock", "iCab", "Iceape", "IceCat", "IceWeasel", "Iron", "K-Meleon", "Lunascape", "Lynx", "<PERSON><PERSON>", "KHTML", "Mosaic", "NetSurf", "OmniWeb", "Opera Tablet", "PaleMoon", "Phoenix", "rekonq", "RockMelt", "SeaMonkey", "Sleipnir", "<PERSON>", "w3m", "IceDragon", "Waterfox", "GSA", "Whale", "Coc Coc", "<PERSON><PERSON><PERSON>", "Facebook", "Avast Secure Browser", "Chrome WebView", "QQBrowser", "Iridium", "AVG Secure Browser", "Basilisk", "Opera GX", "Chrome Headless", "Baidu", "Silk", "Snapchat", "Opera Touch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Tesla", "Brave", "Instagram", "TikTok", "WeChat", "LBBROWSER", "Sogou Explorer", "DuckDuckGo", "Electron"], "os": ["Misc", "Windows", "Linux", "Mac OS", "Android", "iOS", "Windows Phone", "BlackBerry", "Symbian", "<PERSON><PERSON>", "Windows Phone OS", "OpenBSD", "Windows Mobile", "Unix", "Ubuntu", "<PERSON><PERSON>", "Debian", "BeOS", "Hai<PERSON>", "Solar<PERSON>", "Chromium OS", "NetBSD", "FreeBSD", "Slackware", "SUSE", "Gentoo", "Mageia", "Arch", "Red Hat", "CentOS", "Mint", "DragonFly", "<PERSON><PERSON><PERSON><PERSON>", "Mandriva", "Zenwalk", "GNU", "OS/2", "AIX", "QNX", "HP-UX", "RISC OS", "Nintendo", "OpenSolaris", "AmigaOS", "BSD", "OpenVMS", "Raspbian", "Tizen", "Xbox", "PlayStation", "openSUSE"], "matching": {"bot": ["misc"], "ie": ["windows", "mac os", "linux", "unix", "xbox"], "konqueror": ["linux", "freebsd", "fedora", "kubuntu", "slackware", "openbsd", "dragonfly", "windows", "solaris", "netbsd", "suse", "debian"], "opera": ["mac os", "windows", "android", "symbian", "maemo", "ubuntu", "linux", "debian", "mint", "freebsd", "nintendo", "opensolaris", "solaris", "openbsd", "kubuntu", "unix"], "firefox": ["mac os", "windows", "ubuntu", "linux", "fedora", "openbsd", "netbsd", "mageia", "debian", "freebsd", "arch", "gentoo", "red hat", "suse", "centos", "android", "slackware", "mint", "dragonfly", "solaris", "kubuntu", "mandriva", "beos", "zenwalk", "windows phone"], "chrome": ["mac os", "windows", "linux", "openbsd", "chromium os", "netbsd", "freebsd", "slackware", "suse", "ubuntu", "debian", "fedora", "unix", "android", "xbox", "windows phone", "mint", "opensuse"], "mobile chrome": ["android", "ios", "windows phone"], "mobile firefox": ["android", "ios"], "mobile safari": ["ios", "blackberry", "windows", "android", "mac os"], "iemobile": ["windows phone", "windows phone os", "windows"], "safari": ["mac os", "android", "symbian", "windows", "linux", "ios", "bsd", "netbsd", "playstation"], "android browser": ["android", "windows"], "opera mobi": ["android", "symbian", "windows mobile", "windows", "mac os", "linux"], "opera mini": ["ios", "symbian", "android", "windows mobile", "windows", "mac os", "linux", "unix"], "ucbrowser": ["android", "windows"], "puffin": ["android"], "samsung internet": ["android", "linux", "tizen"], "yandex": ["android", "windows", "mac os", "linux", "ios", "fedora"], "miui browser": ["android", "linux"], "edge": ["windows phone", "windows", "mac os", "xbox", "android", "windows mobile", "ios", "linux", "fedora"], "webkit": ["android", "symbian", "mac os", "windows", "linux", "arch", "unix", "ios", "playstation", "xbox", "windows phone"], "blazer": ["windows"], "bolt": ["windows"], "fennec": ["android", "windows", "maemo", "mac os", "linux"], "maemo browser": ["maemo"], "minimo": ["linux", "windows", "openbsd"], "kindle": ["linux", "android"], "skyfire": ["mac os"], "chromium": ["ubuntu", "linux", "netbsd", "raspbian", "windows"], "avant": ["windows"], "maxthon": ["windows"], "arora": ["linux", "windows"], "mozilla": ["windows", "debian", "linux", "ubuntu", "mac os", "openbsd", "beos", "haiku", "solaris", "gentoo", "freebsd", "kubuntu", "fedora", "mint", "red hat", "os/2", "aix", "qnx", "hp-ux", "netbsd", "openvms", "unix"], "epiphany": ["linux", "ubuntu", "openbsd", "freebsd", "suse", "fedora", "debian", "gentoo", "solaris"], "camino": ["mac os"], "chimera": ["mac os"], "comodo dragon": ["windows", "linux"], "conkeror": ["debian", "windows", "linux"], "links": ["linux", "netbsd", "gentoo", "unix", "openbsd", "freebsd", "mac os", "solaris", "debian"], "firebird": ["windows", "mac os", "solaris", "linux"], "swiftfox": ["linux"], "netscape": ["windows", "mac os", "linux", "solaris", "hp-ux", "aix", "xbox"], "flock": ["mac os", "windows", "linux"], "icab": ["mac os", "windows"], "iceape": ["linux"], "icecat": ["linux"], "iceweasel": ["linux", "debian", "gentoo", "ubuntu", "windows"], "iron": ["windows", "linux", "mac os"], "k-meleon": ["windows", "linux", "freebsd"], "lunascape": ["windows"], "lynx": ["gnu"], "midori": ["linux", "freebsd", "windows", "arch", "netbsd"], "khtml": ["windows"], "mosaic": ["windows", "aix", "solaris"], "netsurf": ["risc os", "linux", "netbsd"], "omniweb": ["mac os"], "opera tablet": ["windows", "symbian"], "palemoon": ["windows", "linux"], "phoenix": ["linux", "windows", "mac os"], "rekonq": ["linux"], "rockmelt": ["windows", "mac os"], "seamonkey": ["windows", "linux", "os/2", "mac os", "freebsd", "openbsd", "fedora", "suse", "mandriva", "gentoo", "beos", "haiku", "amigaos", "centos", "red hat", "netbsd"], "sleipnir": ["windows"], "slim": ["windows", "android"], "w3m": ["debian"], "icedragon": ["windows"], "waterfox": ["windows", "linux", "mac os"], "gsa": ["ios"], "whale": ["windows", "mac os", "linux"], "coc coc": ["windows"], "vivaldi": ["windows", "linux", "netbsd", "mac os"], "facebook": ["ios", "android", "mac os"], "avast secure browser": ["windows", "mac os"], "chrome webview": ["android"], "qqbrowser": ["windows", "ios", "android"], "iridium": ["windows"], "avg secure browser": ["windows", "mac os"], "basilisk": ["windows", "linux"], "opera gx": ["windows"], "chrome headless": ["linux", "xbox", "windows"], "baidu": ["android"], "silk": ["mac os", "android", "linux", "playstation"], "snapchat": ["ios"], "opera touch": ["ios", "android"], "tizenbrowser": ["tizen"], "tesla": ["linux", "windows"], "brave": ["android", "mac os", "windows", "gentoo", "mint", "linux", "openbsd", "ios"], "instagram": ["android"], "tiktok": ["android"], "wechat": ["ios", "android"], "lbbrowser": ["windows"], "sogou explorer": ["windows"], "duckduckgo": ["android"], "electron": ["windows"]}}