[{"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en-US; rv:1.9.1.3) Gecko/20090914 Slackware/13.0_stable Firefox/3.5.3", "browser": {"name": "Firefox", "version": "3.5.3", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Gecko", "version": "1.9.1.3"}, "os": {"name": "Slackware", "version": "13.0_stable"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; pl; rv:*******) Gecko/20090911 Slackware Firefox/3.5.2", "browser": {"name": "Firefox", "version": "3.5.2", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Gecko", "version": "*******"}, "os": {"name": "Slackware", "version": "Firefox"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en-US; rv:*******) Gecko/20090803 Slackware Firefox/3.5.2", "browser": {"name": "Firefox", "version": "3.5.2", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Gecko", "version": "*******"}, "os": {"name": "Slackware", "version": "Firefox"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en-US; rv:*******) Gecko/20090803 Firefox/3.5.2 Slackware", "browser": {"name": "Firefox", "version": "3.5.2", "major": "3"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Gecko", "version": "*******"}, "os": {"name": "Slackware"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en-US; rv:*******) Gecko/20090729 Slackware/13.0 Firefox/3.5.2", "browser": {"name": "Firefox", "version": "3.5.2", "major": "3"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "*******"}, "os": {"name": "Slackware", "version": "13.0"}}, {"ua": "Mozilla/5.0 (X11; U; Slackware Linux i686; en-US; rv:********) Gecko/2009042315 Firefox/3.0.10", "browser": {"name": "Firefox", "version": "3.0.10", "major": "3"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "********"}, "os": {"name": "Slackware", "version": "i686"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; pl-PL; rv:*******) Gecko/2008121622 Slackware/2.6.27-PiP Firefox/3.0", "browser": {"name": "Firefox", "version": "3.0", "major": "3"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "*******"}, "os": {"name": "Slackware", "version": "2.6.27-Pi<PERSON>"}}]