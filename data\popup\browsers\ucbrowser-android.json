[{"ua": "Mozilla/5.0 (Linux; U; Android 7.0; en-US; SM-G935F Build/NRD90M) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 UCBrowser/11.3.8.976 U3/0.8.0 Mobile Safari/534.30", "browser": {"name": "UC<PERSON><PERSON><PERSON>", "version": "11.3.8.976", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G935F", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.2; id; SM-G900 Build/KOT49H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 UCBrowser/9.9.2.467 U3/0.8.0 Mobile Safari/534.30", "browser": {"name": "UC<PERSON><PERSON><PERSON>", "version": "9.9.2.467", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G900", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 6.0.1; en-US; SM-G920F Build/LMY47X) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 UCBrowser/10.10.0.796 U3/0.8.0 Mobile Safari/534.30", "browser": {"name": "UC<PERSON><PERSON><PERSON>", "version": "10.10.0.796", "major": "10"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G920F", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 6.0.1; zh-C<PERSON>; F5121 Build/34.0.A.1.247) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/40.0.2214.89 UCBrowser/11.5.1.944 Mobile Safari/537.36", "browser": {"name": "UC<PERSON><PERSON><PERSON>", "version": "11.5.1.944", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "F5121", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "40.0.2214.89"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.2; zh-CN; HUAWEI MT7-TL00 Build/HuaweiMT7-TL00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/40.0.2214.89 UCBrowser/11.3.8.909 Mobile Safari/537.36", "browser": {"name": "UC<PERSON><PERSON><PERSON>", "version": "11.3.8.909", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "MT7-TL00", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "40.0.2214.89"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 7.0; en-US; SM-G935F Build/NRD90M) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 UCBrowser/11.3.8.976 U3/0.8.0 Mobile Safari/534.30", "browser": {"name": "UC<PERSON><PERSON><PERSON>", "version": "11.3.8.976", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G935F", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 7.0; zh-CN; EVA-AL00 Build/HUAWEIEVA-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.108 UCBrowser/11.9.4.974 UWS/2.14.0.2 Mobile Safari/537.36 AliApp(TB/7.7.5) UCBS/2.11.1.1 TTID/227200@taobao_android_7.7.5 WindVane/8.3.0 1080X1794", "browser": {"name": "UC<PERSON><PERSON><PERSON>", "version": "11.9.4.974", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "EVA-AL00", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "57.0.2987.108"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; <PERSON>; Android 12; zh-C<PERSON>; BRA-AL00 Build/HUAWEIBRA-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.108 UCBrowser/12.0.2.995 Mobile Safari/537.36", "browser": {"name": "UC<PERSON><PERSON><PERSON>", "version": "12.0.2.995", "major": "12"}, "cpu": {}, "device": {"type": "mobile", "model": "BRA-AL00", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "57.0.2987.108"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 6.0.1; zh-C<PERSON>; SM-A8000 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/40.0.2214.89 UCBrowser/11.4.1.939 UWS/2.11.0.7 Mobile Safari/537.36 AliApp(TB/6.11.0) UCBS/2.11.1.1 WindVane/8.0.0 1080X1920 GCanvas/1.4.2.21", "browser": {"name": "UC<PERSON><PERSON><PERSON>", "version": "11.4.1.939", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A8000", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "40.0.2214.89"}, "os": {"name": "Android", "version": "6.0.1"}}]