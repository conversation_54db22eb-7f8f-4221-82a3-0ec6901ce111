[{"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-A310F Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Mobile Safari/537.36 OPR/42.7.2246.114996", "browser": {"name": "Opera", "version": "42.7.2246.114996", "major": "42"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A310F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "55.0.2883.91"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Android 2.2.2; Linux; Opera Mobi/ADR-1103311355; U; en; rv:1.9.1.6) Gecko/20091201 Firefox/3.5.6 Opera 11.00", "browser": {"name": "Opera", "version": "<PERSON><PERSON>", "major": ""}, "cpu": {}, "device": {}, "engine": {"name": "Gecko", "version": "1.9.1.6"}, "os": {"name": "Android", "version": "2.2.2"}}, {"ua": "Mozilla/4.0 (compatible; MSIE 8.0; Android 2.2.2; Linux; Opera Mobi/ADR-1103311355; en) Opera 11.00", "browser": {"name": "Opera", "version": "<PERSON><PERSON>", "major": ""}, "cpu": {}, "device": {}, "engine": {}, "os": {"name": "Android", "version": "2.2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 5.0.2; zh-C<PERSON>; Redmi Note 3 Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 OPR/11.2.3.102637 Mobile Safari/537.36", "browser": {"name": "Opera", "version": "11.2.3.102637", "major": "11"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 3", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "WebKit", "version": "537.36"}, "os": {"name": "Android", "version": "5.0.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-A310F Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Mobile Safari/537.36 OPR/42.7.2246.114996", "browser": {"name": "Opera", "version": "42.7.2246.114996", "major": "42"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A310F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "55.0.2883.91"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.99 Safari/537.36 OPR/35.0.2070.100283", "browser": {"name": "Opera", "version": "35.0.2070.100283", "major": "35"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "48.0.2564.99"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.158 Safari/537.36 OPR/47.3.2249.130976", "browser": {"name": "Opera", "version": "47.3.2249.130976", "major": "47"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "66.0.3359.158"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.116 Safari/537.36 OPR/55.2.2719.50740", "browser": {"name": "Opera", "version": "55.2.2719.50740", "major": "55"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36 OPR/56.0.2780.51441", "browser": {"name": "Opera", "version": "56.0.2780.51441", "major": "56"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36 OPR/57.0.2830.52476", "browser": {"name": "Opera", "version": "57.0.2830.52476", "major": "57"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36 OPR/57.1.2830.52480", "browser": {"name": "Opera", "version": "57.1.2830.52480", "major": "57"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36 OPR/58.2.2878.53403", "browser": {"name": "Opera", "version": "58.2.2878.53403", "major": "58"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.90 Safari/537.36 OPR/52.0.2486.138175", "browser": {"name": "Opera", "version": "52.0.2486.138175", "major": "52"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.116 Safari/537.36 OPR/55.2.2719.50740", "browser": {"name": "Opera", "version": "55.2.2719.50740", "major": "55"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36 OPR/61.2.3076.56749", "browser": {"name": "Opera", "version": "61.2.3076.56749", "major": "61"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Mobile Safari/537.36 OPR/51.3.2461.138727", "browser": {"name": "Opera", "version": "51.3.2461.138727", "major": "51"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "72.0.3626.121"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Mobile Safari/537.36 OPR/56.1.2780.51589", "browser": {"name": "Opera", "version": "56.1.2780.51589", "major": "56"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Mobile Safari/537.36 OPR/57.1.2830.52480", "browser": {"name": "Opera", "version": "57.1.2830.52480", "major": "57"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Mobile Safari/537.36 OPR/58.1.2878.53288", "browser": {"name": "Opera", "version": "58.1.2878.53288", "major": "58"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; CPH1801) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/84.0.4147.37Mobile Safari/537.36 OPR/58.2.2878.53403", "browser": {"name": "Opera", "version": "58.2.2878.53403", "major": "58"}, "cpu": {}, "device": {"type": "mobile", "model": "CPH1801", "vendor": "OPPO"}, "engine": {"name": "Blink", "version": "84.0.4147.37Mobile"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190305.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.99 Safari/537.36 OPR/50.3.2426.136976", "browser": {"name": "Opera", "version": "50.3.2426.136976", "major": "50"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "71.0.3578.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190405.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36 OPR/51.2.2461.137690", "browser": {"name": "Opera", "version": "51.2.2461.137690", "major": "51"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "72.0.3626.121"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190405.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36 OPR/51.3.2461.138727", "browser": {"name": "Opera", "version": "51.3.2461.138727", "major": "51"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "72.0.3626.121"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190505.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36 OPR/51.3.2461.138727", "browser": {"name": "Opera", "version": "51.3.2461.138727", "major": "51"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "72.0.3626.121"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.90 Safari/537.36 OPR/52.4.2517.140781", "browser": {"name": "Opera", "version": "52.4.2517.140781", "major": "52"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "73.0.3683.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 OPR/53.0.2569.141117", "browser": {"name": "Opera", "version": "53.0.2569.141117", "major": "53"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 OPR/53.0.2569.141117", "browser": {"name": "Opera", "version": "53.0.2569.141117", "major": "53"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 OPR/53.1.2569.142848", "browser": {"name": "Opera", "version": "53.1.2569.142848", "major": "53"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.132 Safari/537.36 OPR/54.2.2672.49907", "browser": {"name": "Opera", "version": "54.2.2672.49907", "major": "54"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.116 Safari/537.36 OPR/55.0.2719.50522", "browser": {"name": "Opera", "version": "55.0.2719.50522", "major": "55"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.116 Safari/537.36 OPR/55.0.2719.50560", "browser": {"name": "Opera", "version": "55.0.2719.50560", "major": "55"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.116 Safari/537.36 OPR/55.1.2719.50626", "browser": {"name": "Opera", "version": "55.1.2719.50626", "major": "55"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.116 Safari/537.36 OPR/55.2.2719.50740", "browser": {"name": "Opera", "version": "55.2.2719.50740", "major": "55"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36 OPR/56.0.2780.51368", "browser": {"name": "Opera", "version": "56.0.2780.51368", "major": "56"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36 OPR/56.1.2780.51589", "browser": {"name": "Opera", "version": "56.1.2780.51589", "major": "56"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36 OPR/57.1.2830.52480", "browser": {"name": "Opera", "version": "57.1.2830.52480", "major": "57"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Safari/537.36 OPR/57.2.2830.52651", "browser": {"name": "Opera", "version": "57.2.2830.52651", "major": "57"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.162"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.117 Safari/537.36 OPR/58.0.2864.52917", "browser": {"name": "Opera", "version": "58.0.2864.52917", "major": "58"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.117"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36 OPR/58.1.2878.53288", "browser": {"name": "Opera", "version": "58.1.2878.53288", "major": "58"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36 OPR/58.2.2878.53403", "browser": {"name": "Opera", "version": "58.2.2878.53403", "major": "58"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.83 Safari/537.36 OPR/59.0.2904.53502", "browser": {"name": "Opera", "version": "59.0.2904.53502", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.83"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36 OPR/59.0.2926.53920", "browser": {"name": "Opera", "version": "59.0.2926.53920", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36 OPR/59.1.2926.54067", "browser": {"name": "Opera", "version": "59.1.2926.54067", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.101 Safari/537.36 OPR/60.0.3004.55063", "browser": {"name": "Opera", "version": "60.0.3004.55063", "major": "60"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Safari/537.36 OPR/60.1.3004.55238", "browser": {"name": "Opera", "version": "60.1.3004.55238", "major": "60"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Safari/537.36 OPR/60.2.3004.55409", "browser": {"name": "Opera", "version": "60.2.3004.55409", "major": "60"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Safari/537.36 OPR/60.3.3004.55692", "browser": {"name": "Opera", "version": "60.3.3004.55692", "major": "60"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36 OPR/61.1.3076.56625", "browser": {"name": "Opera", "version": "61.1.3076.56625", "major": "61"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36 OPR/61.2.3076.56749", "browser": {"name": "Opera", "version": "61.2.3076.56749", "major": "61"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36 OPR/62.0.3146.57357", "browser": {"name": "Opera", "version": "62.0.3146.57357", "major": "62"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36 OPR/62.1.3146.57513", "browser": {"name": "Opera", "version": "62.1.3146.57513", "major": "62"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36 OPR/62.2.3146.57547", "browser": {"name": "Opera", "version": "62.2.3146.57547", "major": "62"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36 OPR/62.3.3146.57763", "browser": {"name": "Opera", "version": "62.3.3146.57763", "major": "62"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36 OPR/53.1.2569.142848", "browser": {"name": "Opera", "version": "53.1.2569.142848", "major": "53"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; TESLA MediaBox X900 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Safari/537.36 OPR/60.3.3004.55692", "browser": {"name": "Opera", "version": "60.3.3004.55692", "major": "60"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA MediaBox X900 Pro"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36 OPR/59.1.2926.54067", "browser": {"name": "Opera", "version": "59.1.2926.54067", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Safari/537.36 OPR/60.3.3004.55692", "browser": {"name": "Opera", "version": "60.3.3004.55692", "major": "60"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36 OPR/61.2.3076.56749", "browser": {"name": "Opera", "version": "61.2.3076.56749", "major": "61"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 5.1; Tesla Build/LMY47D; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/39.0.0.0 Safari/537.36 OPR/32.0.2254.122976", "browser": {"name": "Opera", "version": "32.0.2254.122976", "major": "32"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 5.1; Tesla Build/LMY47D; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/39.0.0.0 Safari/537.36 OPR/46.0.2254.145391", "browser": {"name": "Opera", "version": "46.0.2254.145391", "major": "46"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 7.0; Tesla_SP3.3 Lite Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36 OPR/50.0.2254.149182", "browser": {"name": "Opera", "version": "50.0.2254.149182", "major": "50"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/64.0.3282.137 Mobile Safari/537.36 OPR/50.0.2254.149182", "browser": {"name": "Opera", "version": "50.0.2254.149182", "major": "50"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "64.0.3282.137"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Mobile Safari/537.36 OPR/47.0.2254.146760", "browser": {"name": "Opera", "version": "47.0.2254.146760", "major": "47"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 7.0; Tesla_SP6.3 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Mobile Safari/537.36 OPR/54.0.2254.56148", "browser": {"name": "Opera", "version": "54.0.2254.56148", "major": "54"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 7.0; Tesla_SP9.1L Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Mobile Safari/537.36 OPR/52.1.2254.54298", "browser": {"name": "Opera", "version": "52.1.2254.54298", "major": "52"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1L"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 8.1.0; Pixel C Build/OPM4.171019.016.C1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.136 Safari/537.36 OPR/40.1.2254.138129", "browser": {"name": "Opera", "version": "40.1.2254.138129", "major": "40"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.136"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 8.1.0; Pixel C Build/OPM4.171019.016.C1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.136 Safari/537.36 OPR/42.0.2254.139276", "browser": {"name": "Opera", "version": "42.0.2254.139276", "major": "42"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.136"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 8.1.0; Pixel C Build/OPM4.171019.016.C1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Safari/537.36 OPR/42.0.2254.139276", "browser": {"name": "Opera", "version": "42.0.2254.139276", "major": "42"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 8.1.0; Pixel C Build/OPM4.171019.021.D1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/76.0.3809.132 Safari/537.36 OPR/44.1.2254.142553", "browser": {"name": "Opera", "version": "44.1.2254.142553", "major": "44"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 8.1.0; Pixel C Build/OPM4.171019.021.Z1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.110 Safari/537.36 OPR/51.0.2254.150807", "browser": {"name": "Opera", "version": "51.0.2254.150807", "major": "51"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 8.1.0; Tesla_SP9_2 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Mobile Safari/537.36 OPR/8.0.2254.54384", "browser": {"name": "Opera", "version": "8.0.2254.54384", "major": "8"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; VOG-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36 OPR/63.0.3216.58473", "browser": {"name": "Opera", "version": "63.0.3216.58473", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "VOG-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "90.0.4430.91"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G970F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36 OPR/63.0.3216.58473", "browser": {"name": "Opera", "version": "63.0.3216.58473", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "90.0.4430.91"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-N975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36 OPR/63.0.3216.58473", "browser": {"name": "Opera", "version": "63.0.3216.58473", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N975F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "90.0.4430.91"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; NEO-U9-H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Safari/537.36 OPR/63.3.3216.58675", "browser": {"name": "Opera", "version": "63.3.3216.58675", "major": "63"}, "cpu": {}, "device": {"type": "tablet", "model": "NEO-U9-H"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.4; Lenovo A390_ROW Build/IMM76I) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.107 Mobile Safari/537.36 OPR/29.0.1809.93516", "browser": {"name": "Opera", "version": "29.0.1809.93516", "major": "29"}, "cpu": {}, "device": {"type": "mobile", "model": "A390_ROW", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "42.0.2311.107"}, "os": {"name": "Android", "version": "4.0.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; LE2100) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Mobile Safari/537.36 OPR/69.0.3606.64982", "browser": {"name": "Opera", "version": "69.0.3606.64982", "major": "69"}, "cpu": {}, "device": {"type": "mobile", "model": "LE2100"}, "engine": {"name": "Blink", "version": "100.0.4896.127"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 8.1.0; Pixel C Build/OPM4.171019.021.Z1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.110 Safari/537.36 OPR/51.0.2254.150807", "browser": {"name": "Opera", "version": "51.0.2254.150807", "major": "51"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Mobile Safari/537.36 OPR/89.0.0.0", "browser": {"name": "Opera", "version": "89.0.0.0", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "135.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36 OPR/59.1.2926.54067", "browser": {"name": "Opera", "version": "59.1.2926.54067", "major": "59"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "8.1.0"}}]