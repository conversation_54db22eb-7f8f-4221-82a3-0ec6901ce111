[{"ua": "Mozilla/5.0 (Babusca 5.2;GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Tesla QtCarBrowser Chrome/90 SEB/3.11", "browser": {"name": "Tesla"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "90"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (Babusca 5.2;X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Tesla QtCarBrowser Chrome/90 SEB/3.11", "browser": {"name": "Tesla"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "90"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/71.0.3578.98 Chrome/71.0.3578.98 Safari/537.36 Tesla QtCarBrowser", "browser": {"name": "Tesla"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "71.0.3578.98"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/72.0.3626.122 Chrome/72.0.3626.122 Safari/537.36 Tesla QtCarBrowser", "browser": {"name": "Tesla"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "72.0.3626.122"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/73.0.3683.101 Chrome/73.0.3683.101 Safari/537.36 Tesla QtCarBrowser", "browser": {"name": "Tesla"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "73.0.3683.101"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/74.0.3729.131 Chrome/74.0.3729.131 Safari/537.36 Tesla QtCarBrowser", "browser": {"name": "Tesla"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "74.0.3729.131"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/74.0.3729.131 Chrome/74.0.3729.131 Safari/537.36 Tesla QtCarBrowser,gzip(gfe)", "browser": {"name": "Tesla"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "74.0.3729.131"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.32.2.11-1a8580f", "browser": {"name": "Tesla", "version": "2019.32.2.11-1a8580f", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.32.2.12-4e82926", "browser": {"name": "Tesla", "version": "2019.32.2.12-4e82926", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.32.10-749a9e9", "browser": {"name": "Tesla", "version": "2019.32.10-749a9e9", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.32.10.1-0874034", "browser": {"name": "Tesla", "version": "2019.32.10.1-0874034", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.32.10.2-283497f", "browser": {"name": "Tesla", "version": "2019.32.10.2-283497f", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.32.11-bac8c51", "browser": {"name": "Tesla", "version": "2019.32.11-bac8c51", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.32.11.1-d39e85a", "browser": {"name": "Tesla", "version": "2019.32.11.1-d39e85a", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.32.12.1-641e9fa", "browser": {"name": "Tesla", "version": "2019.32.12.1-641e9fa", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.32.12.2-58f3b76", "browser": {"name": "Tesla", "version": "2019.32.12.2-58f3b76", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.32.12.4-a3dc21e", "browser": {"name": "Tesla", "version": "2019.32.12.4-a3dc21e", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.35.102-a59cb73", "browser": {"name": "Tesla", "version": "2019.35.102-a59cb73", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.35.103-0166952", "browser": {"name": "Tesla", "version": "2019.35.103-0166952", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.35.106-f83b666", "browser": {"name": "Tesla", "version": "2019.35.106-f83b666", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.35.108-3554e19", "browser": {"name": "Tesla", "version": "2019.35.108-3554e19", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.35.109-ccbb28a", "browser": {"name": "Tesla", "version": "2019.35.109-ccbb28a", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.36.1-0164b9a", "browser": {"name": "Tesla", "version": "2019.36.1-0164b9a", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.36.2.1-ea322ad", "browser": {"name": "Tesla", "version": "2019.36.2.1-ea322ad", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.36.2.2-186de86", "browser": {"name": "Tesla", "version": "2019.36.2.2-186de86", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.36.2.3-4a358fb", "browser": {"name": "Tesla", "version": "2019.36.2.3-4a358fb", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.36.2.4-fc422c3", "browser": {"name": "Tesla", "version": "2019.36.2.4-fc422c3", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.40.2-36f8355b356a", "browser": {"name": "Tesla", "version": "2019.40.2-36f8355b356a", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.40.2.1-38f55d9f9205", "browser": {"name": "Tesla", "version": "2019.40.2.1-38f55d9f9205", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.40.50.1-7fefc12d805a", "browser": {"name": "Tesla", "version": "2019.40.50.1-7fefc12d805a", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.40.50.5-79b51bc76a61", "browser": {"name": "Tesla", "version": "2019.40.50.5-79b51bc76a61", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/75.0.3770.100 Chrome/75.0.3770.100 Safari/537.36 Tesla/2019.40.50.7-ad132c7b057e", "browser": {"name": "Tesla", "version": "2019.40.50.7-ad132c7b057e", "major": "2019"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "75.0.3770.100"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.88 Chrome/79.0.3945.88 Safari/537.36 Tesla/2020.4-bec232c2c946", "browser": {"name": "Tesla", "version": "2020.4-bec232c2c946", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.88"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.88 Chrome/79.0.3945.88 Safari/537.36 Tesla/2020.4.1-4a4ad401858f", "browser": {"name": "Tesla", "version": "2020.4.1-4a4ad401858f", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.88"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.88 Chrome/79.0.3945.88 Safari/537.36 Tesla/2020.4.2-b343447a1bc6", "browser": {"name": "Tesla", "version": "2020.4.2-b343447a1bc6", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.88"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.5.10.4-77e0af1e9a64", "browser": {"name": "Tesla", "version": "2020.5.10.4-77e0af1e9a64", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.8.1-ae1963092ff8", "browser": {"name": "Tesla", "version": "2020.8.1-ae1963092ff8", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.8.2-dc9bc402da23", "browser": {"name": "Tesla", "version": "2020.8.2-dc9bc402da23", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.8.3-7cbc6c4cd1c8", "browser": {"name": "Tesla", "version": "2020.8.3-7cbc6c4cd1c8", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.11.103-aac2ba786808", "browser": {"name": "Tesla", "version": "2020.11.103-aac2ba786808", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.11.103.5-c15dd52044a4", "browser": {"name": "Tesla", "version": "2020.11.103.5-c15dd52044a4", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.11.105-954ebf2b15b7", "browser": {"name": "Tesla", "version": "2020.11.105-954ebf2b15b7", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.11.105.1-cf2b072f822c", "browser": {"name": "Tesla", "version": "2020.11.105.1-cf2b072f822c", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.12-4fbcc4b942a8", "browser": {"name": "Tesla", "version": "2020.12-4fbcc4b942a8", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.12.1-299292ad7782", "browser": {"name": "Tesla", "version": "2020.12.1-299292ad7782", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.12.5-e2179e0650f0", "browser": {"name": "Tesla", "version": "2020.12.5-e2179e0650f0", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.12.6-c9e3d0ebfbe3", "browser": {"name": "Tesla", "version": "2020.12.6-c9e3d0ebfbe3", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.12.10-e0ccfda3d911", "browser": {"name": "Tesla", "version": "2020.12.10-e0ccfda3d911", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.12.11.1-81280b81af0a", "browser": {"name": "Tesla", "version": "2020.12.11.1-81280b81af0a", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.12.11.2-3ec2d07f7ee5", "browser": {"name": "Tesla", "version": "2020.12.11.2-3ec2d07f7ee5", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.16.2-a0c3c853bd63", "browser": {"name": "Tesla", "version": "2020.16.2-a0c3c853bd63", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.16.2.1-e99c70fff409", "browser": {"name": "Tesla", "version": "2020.16.2.1-e99c70fff409", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.16.3-53d19559a8a7", "browser": {"name": "Tesla", "version": "2020.16.3-53d19559a8a7", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.20.1-552e77693750", "browser": {"name": "Tesla", "version": "2020.20.1-552e77693750", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.20.5-b1e18f9f15b0", "browser": {"name": "Tesla", "version": "2020.20.5-b1e18f9f15b0", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.20.12-d2c8a3e110f4", "browser": {"name": "Tesla", "version": "2020.20.12-d2c8a3e110f4", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.20.13-bd621ce1cc98", "browser": {"name": "Tesla", "version": "2020.20.13-bd621ce1cc98", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.20.17-301e3e6efcc9", "browser": {"name": "Tesla", "version": "2020.20.17-301e3e6efcc9", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.24.5.1-8971f7ae36b5", "browser": {"name": "Tesla", "version": "2020.24.5.1-8971f7ae36b5", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.24.6.1-eefccf854c65", "browser": {"name": "Tesla", "version": "2020.24.6.1-eefccf854c65", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.24.6.2-ef8e85c605a7", "browser": {"name": "Tesla", "version": "2020.24.6.2-ef8e85c605a7", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.24.6.3-78cfd2a93629", "browser": {"name": "Tesla", "version": "2020.24.6.3-78cfd2a93629", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.24.6.4-b9a45d992a14", "browser": {"name": "Tesla", "version": "2020.24.6.4-b9a45d992a14", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.24.6.4-b9a45d992a14,gzip(gfe)", "browser": {"name": "Tesla", "version": "2020.24.6.4-b9a45d992a14", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.24.6.5-250a46d88d9c", "browser": {"name": "Tesla", "version": "2020.24.6.5-250a46d88d9c", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.24.6.9-9a5f3cf2c682", "browser": {"name": "Tesla", "version": "2020.24.6.9-9a5f3cf2c682", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.27.101.1-80812018fc43", "browser": {"name": "Tesla", "version": "2020.27.101.1-80812018fc43", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.28.1-9bc62dbe01c7", "browser": {"name": "Tesla", "version": "2020.28.1-9bc62dbe01c7", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.28.2-91220d23640f", "browser": {"name": "Tesla", "version": "2020.28.2-91220d23640f", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.28.5-4a6fbab3952d", "browser": {"name": "Tesla", "version": "2020.28.5-4a6fbab3952d", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.28.6-35849ea453e9", "browser": {"name": "Tesla", "version": "2020.28.6-35849ea453e9", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.32.1-d702790a3d0b", "browser": {"name": "Tesla", "version": "2020.32.1-d702790a3d0b", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.32.2-108daa2c1277", "browser": {"name": "Tesla", "version": "2020.32.2-108daa2c1277", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.32.3-b9bd4364fd17", "browser": {"name": "Tesla", "version": "2020.32.3-b9bd4364fd17", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.32.4-fcd99607fda0", "browser": {"name": "Tesla", "version": "2020.32.4-fcd99607fda0", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.32.5-41d94f79b976", "browser": {"name": "Tesla", "version": "2020.32.5-41d94f79b976", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.35.101-9613b4701347", "browser": {"name": "Tesla", "version": "2020.35.101-9613b4701347", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.35.202.1-97514870366a", "browser": {"name": "Tesla", "version": "2020.35.202.1-97514870366a", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.36-2b8efaffb131", "browser": {"name": "Tesla", "version": "2020.36-2b8efaffb131", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.36.3-b4f3e60addfa", "browser": {"name": "Tesla", "version": "2020.36.3-b4f3e60addfa", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.36.3.1-58d6902aeb93", "browser": {"name": "Tesla", "version": "2020.36.3.1-58d6902aeb93", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.36.4-c5fd2b034104", "browser": {"name": "Tesla", "version": "2020.36.4-c5fd2b034104", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.36.10-010e3e5a2863", "browser": {"name": "Tesla", "version": "2020.36.10-010e3e5a2863", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.36.11-ccacdb181f16", "browser": {"name": "Tesla", "version": "2020.36.11-ccacdb181f16", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.36.12-8a49f93c7445", "browser": {"name": "Tesla", "version": "2020.36.12-8a49f93c7445", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.36.16-3e9e4e8dd287", "browser": {"name": "Tesla", "version": "2020.36.16-3e9e4e8dd287", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.39.101-5695342c1a40", "browser": {"name": "Tesla", "version": "2020.39.101-5695342c1a40", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.39.102-6abc75d6e6b9", "browser": {"name": "Tesla", "version": "2020.39.102-6abc75d6e6b9", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.39.102.1-b7a824a82bb0", "browser": {"name": "Tesla", "version": "2020.39.102.1-b7a824a82bb0", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.39.110-f20ebf7ea42a", "browser": {"name": "Tesla", "version": "2020.39.110-f20ebf7ea42a", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.40-13212b4033be", "browser": {"name": "Tesla", "version": "2020.40-13212b4033be", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.40.3-8dcf2c39be86", "browser": {"name": "Tesla", "version": "2020.40.3-8dcf2c39be86", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.40.4-ffa5212dba76", "browser": {"name": "Tesla", "version": "2020.40.4-ffa5212dba76", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.40.4.5-1450abc0cd10", "browser": {"name": "Tesla", "version": "2020.40.4.5-1450abc0cd10", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.40.7-a705cb3bb83b", "browser": {"name": "Tesla", "version": "2020.40.7-a705cb3bb83b", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.40.8-9744d0c0d399", "browser": {"name": "Tesla", "version": "2020.40.8-9744d0c0d399", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.40.9-507cb67a95c8", "browser": {"name": "Tesla", "version": "2020.40.9-507cb67a95c8", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.44-21f32b9118a0", "browser": {"name": "Tesla", "version": "2020.44-21f32b9118a0", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.44-f45a383c70ee", "browser": {"name": "Tesla", "version": "2020.44-f45a383c70ee", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.44.10-da17aa4391bb", "browser": {"name": "Tesla", "version": "2020.44.10-da17aa4391bb", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.44.10.1-955dc1dd145e", "browser": {"name": "Tesla", "version": "2020.44.10.1-955dc1dd145e", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.44.15-19675f719e84", "browser": {"name": "Tesla", "version": "2020.44.15-19675f719e84", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.44.25-0ba7ea36671e", "browser": {"name": "Tesla", "version": "2020.44.25-0ba7ea36671e", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.47.101-cbc6d150dafb", "browser": {"name": "Tesla", "version": "2020.47.101-cbc6d150dafb", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.47.101.1-053667a9187c", "browser": {"name": "Tesla", "version": "2020.47.101.1-053667a9187c", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.47.102-1c9107c83ff1", "browser": {"name": "Tesla", "version": "2020.47.102-1c9107c83ff1", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.47.103.1-75b8d8172f81", "browser": {"name": "Tesla", "version": "2020.47.103.1-75b8d8172f81", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.47.104-f0c38cc0a86d", "browser": {"name": "Tesla", "version": "2020.47.104-f0c38cc0a86d", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48-21edf5e9ee1d", "browser": {"name": "Tesla", "version": "2020.48-21edf5e9ee1d", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48-37c007875580", "browser": {"name": "Tesla", "version": "2020.48-37c007875580", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48-40c9e800a606", "browser": {"name": "Tesla", "version": "2020.48-40c9e800a606", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48-360859736cf1", "browser": {"name": "Tesla", "version": "2020.48-360859736cf1", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48-baf7bed513ef", "browser": {"name": "Tesla", "version": "2020.48-baf7bed513ef", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48.5-94f659f7cfc1", "browser": {"name": "Tesla", "version": "2020.48.5-94f659f7cfc1", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48.10-f8900cddd03a", "browser": {"name": "Tesla", "version": "2020.48.10-f8900cddd03a", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48.12.1-3095698c8a55", "browser": {"name": "Tesla", "version": "2020.48.12.1-3095698c8a55", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48.26-e3178ea250ba", "browser": {"name": "Tesla", "version": "2020.48.26-e3178ea250ba", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48.30-040912887bad", "browser": {"name": "Tesla", "version": "2020.48.30-040912887bad", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48.35.5-842a95863251", "browser": {"name": "Tesla", "version": "2020.48.35.5-842a95863251", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48.35.9-2e4c861dd41e", "browser": {"name": "Tesla", "version": "2020.48.35.9-2e4c861dd41e", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.48.37.1-332984f3b038", "browser": {"name": "Tesla", "version": "2020.48.37.1-332984f3b038", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2020.52-d7552b2ebe50", "browser": {"name": "Tesla", "version": "2020.52-d7552b2ebe50", "major": "2020"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4-46c9cead8dbc", "browser": {"name": "Tesla", "version": "2021.4-46c9cead8dbc", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4.3-568eaf33670d", "browser": {"name": "Tesla", "version": "2021.4.3-568eaf33670d", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4.5-c5885dc82b40", "browser": {"name": "Tesla", "version": "2021.4.5-c5885dc82b40", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4.6-e44dbab07c42", "browser": {"name": "Tesla", "version": "2021.4.6-e44dbab07c42", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4.6.1-3ab64eb29b43", "browser": {"name": "Tesla", "version": "2021.4.6.1-3ab64eb29b43", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4.6.2-99d8c9b7aeee", "browser": {"name": "Tesla", "version": "2021.4.6.2-99d8c9b7aeee", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4.10-f943543c17db", "browser": {"name": "Tesla", "version": "2021.4.10-f943543c17db", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4.10.1-735562538b27", "browser": {"name": "Tesla", "version": "2021.4.10.1-735562538b27", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4.11-8af6a4f84c84", "browser": {"name": "Tesla", "version": "2021.4.11-8af6a4f84c84", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4.11.1-c4a54e10976d", "browser": {"name": "Tesla", "version": "2021.4.11.1-c4a54e10976d", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4.12-ec1e7082b41c", "browser": {"name": "Tesla", "version": "2021.4.12-ec1e7082b41c", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4.12.2-810872f8809b", "browser": {"name": "Tesla", "version": "2021.4.12.2-810872f8809b", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.4.13-841d88c07ee9", "browser": {"name": "Tesla", "version": "2021.4.13-841d88c07ee9", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/2021.8-6d5be4f0ec6f", "browser": {"name": "Tesla", "version": "2021.8-6d5be4f0ec6f", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/88.0.4324.150 Chrome/88.0.4324.150 Safari/537.36 Tesla/2021.4.15-4acfc3067b51", "browser": {"name": "Tesla", "version": "2021.4.15-4acfc3067b51", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "88.0.4324.150"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/88.0.4324.150 Chrome/88.0.4324.150 Safari/537.36 Tesla/2021.4.15.1-c227d6fb1ecf", "browser": {"name": "Tesla", "version": "2021.4.15.1-c227d6fb1ecf", "major": "2021"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "88.0.4324.150"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/601.1 (KHTML, like Gecko) Tesla QtCarBrowser Safari/601.1", "browser": {"name": "Tesla"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "WebKit", "version": "601.1"}, "os": {"name": "Linux"}}]