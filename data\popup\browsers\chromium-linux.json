[{"ua": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/534.30 (KHTML, like Gecko) Chrome/12.0.742.91 Chromium/12.0.742.91 Safari/534.30", "browser": {"name": "Chromium", "version": "12.0.742.91", "major": "12"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Linux", "version": "i686"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) snap Chromium/74.0.3729.131 Chrome/74.0.3729.131 Safari/537.36", "browser": {"name": "Chromium", "version": "74.0.3729.131", "major": "74"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "74.0.3729.131"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) snap Chromium/80.0.3987.132 Chrome/80.0.3987.132 Safari/537.36", "browser": {"name": "Chromium", "version": "80.0.3987.132", "major": "80"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) snap Chromium/80.0.3987.132 Chrome/80.0.3987.132 Safari/537.36", "browser": {"name": "Chromium", "version": "80.0.3987.132", "major": "80"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) snap Chromium/80.0.3987.132 Chrome/80.0.3987.132 Safari/537.36", "browser": {"name": "Chromium", "version": "80.0.3987.132", "major": "80"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "80.0.3987.132"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/feature/ui-plus-fsd-0b6dd4d4db1f", "browser": {"name": "Chromium", "version": "79.0.3945.130", "major": "79"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/feature/ui-plus-fsd-0dd2b30798bf", "browser": {"name": "Chromium", "version": "79.0.3945.130", "major": "79"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/79.0.3945.130 Chrome/79.0.3945.130 Safari/537.36 Tesla/feature/ui-plus-fsd-a6439fd47d02", "browser": {"name": "Chromium", "version": "79.0.3945.130", "major": "79"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "79.0.3945.130"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; GNU/Linux) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/88.0.4324.150 Chrome/88.0.4324.150 Safari/537.36 Tesla/DEV-BUILD-4d1a3f465b3a", "browser": {"name": "Chromium", "version": "88.0.4324.150", "major": "88"}, "cpu": {}, "device": {"type": "embedded", "vendor": "Tesla"}, "engine": {"name": "Blink", "version": "88.0.4324.150"}, "os": {"name": "Linux"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64; Chromium GOST) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36", "browser": {"name": "Chromium", "version": "GOST", "major": ""}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "111.0.0.0"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64; Chromium GOST) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36", "browser": {"name": "Chromium", "version": "GOST", "major": ""}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "117.0.0.0"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64; Chromium GOST) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36", "browser": {"name": "Chromium", "version": "GOST", "major": ""}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "117.0.0.0"}, "os": {"name": "Linux", "version": "x86_64"}}]