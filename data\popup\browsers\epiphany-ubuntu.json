[{"ua": "Mozilla/5.0 (X11; U; Linux x86_64; it-it) AppleWebKit/534.26+ (KHTML, like Gecko) Ubuntu/11.04 Epiphany/2.30.6", "browser": {"name": "Epiphany", "version": "2.30.6", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "WebKit", "version": "534.26"}, "os": {"name": "Ubuntu", "version": "11.04"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en; rv:********) Gecko/20080528 Ubuntu/9.10 (karmic) Epiphany/2.22 Firefox/3.0", "browser": {"name": "Epiphany", "version": "2.22", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Gecko", "version": "********"}, "os": {"name": "Ubuntu", "version": "9.10"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en; rv:*******) Gecko/20061201 Epiphany/2.18 Firefox/******* (Ubuntu-feisty)", "browser": {"name": "Epiphany", "version": "2.18", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Gecko", "version": "*******"}, "os": {"name": "Ubuntu", "version": "feisty"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; en; rv:*******) Gecko/20061201 Epiphany/2.18 Firefox/******* (Ubuntu-feisty)", "browser": {"name": "Epiphany", "version": "2.18", "major": "2"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Gecko", "version": "*******"}, "os": {"name": "Ubuntu", "version": "feisty"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en; rv:*******) Gecko/20061201 Epiphany/2.18 Firefox/******* (Ubuntu-feisty)", "browser": {"name": "Epiphany", "version": "2.18", "major": "2"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "*******"}, "os": {"name": "Ubuntu", "version": "feisty"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en; rv:*******) Gecko/20060928 Epiphany/2.14 (Ubuntu)", "browser": {"name": "Epiphany", "version": "2.14", "major": "2"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "*******"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en-US; rv:*******) Gecko/20060731 Ubuntu/dapper-security Epiphany/2.14 Firefox/*******", "browser": {"name": "Epiphany", "version": "2.14", "major": "2"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "*******"}, "os": {"name": "Ubuntu", "version": "dapper-security"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en-US; rv:*******) Gecko/20060608 Ubuntu/dapper-security Epiphany/2.14 Firefox/*******", "browser": {"name": "Epiphany", "version": "2.14", "major": "2"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "*******"}, "os": {"name": "Ubuntu", "version": "dapper-security"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en-US; rv:*******) Gecko/20060523 Ubuntu/dapper Epiphany/2.14 Firefox/*******", "browser": {"name": "Epiphany", "version": "2.14", "major": "2"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "*******"}, "os": {"name": "Ubuntu", "version": "dapper"}}, {"ua": "Mozilla/5.0 (X11; U; Linux x86_64; fr; rv:1.7.12) Gecko/20051010 Epiphany/1.8.2 (Ubuntu) (Ubuntu package 1.0.7)", "browser": {"name": "Epiphany", "version": "1.8.2", "major": "1"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Gecko", "version": "1.7.12"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.7.12) Gecko/20051010 Epiphany/1.8.2 (Ubuntu) (Ubuntu package 1.0.7)", "browser": {"name": "Epiphany", "version": "1.8.2", "major": "1"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "1.7.12"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.7.3) Gecko/20040924 Epiphany/1.4.4 (Ubuntu)", "browser": {"name": "Epiphany", "version": "1.4.4", "major": "1"}, "cpu": {"architecture": "ia32"}, "device": {}, "engine": {"name": "Gecko", "version": "1.7.3"}, "os": {"name": "Ubuntu"}}, {"ua": "Mozilla/5.0 (X11; U; Linux i586; en-US; rv:1.7.3) Gecko/20040924 Epiphany/1.4.4 (Ubuntu)", "browser": {"name": "Epiphany", "version": "1.4.4", "major": "1"}, "cpu": {}, "device": {}, "engine": {"name": "Gecko", "version": "1.7.3"}, "os": {"name": "Ubuntu"}}]