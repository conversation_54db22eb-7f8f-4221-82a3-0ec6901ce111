[{"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Safari/537.36 trill_2021805060 JsSdk/1.0 NetType/WIFI Channel/googleplay AppName/musical_ly app_version/18.5.6 ByteLocale/de-DE ByteFullLocale/de-DE Region/DE", "browser": {"name": "TikTok", "version": "18.5.6", "major": "18"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}]