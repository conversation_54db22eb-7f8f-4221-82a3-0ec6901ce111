[{"ua": "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; Bingbot/2.0; +http://www.bing.com/bingbot.htm)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "DuckDuckBot/1.0; (+http://duckduckgo.com/duckduckbot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; Exabot/3.0; +http://www.exabot.com/go/robot)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; DotBot/1.1; http://www.opensiteexplorer.org/dotbot, <EMAIL>)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; Qwantify/Bleriot/1.1; +https://help.qwant.com/bot)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; SemrushBot/3~bl; +http://www.semrush.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; DotBot/1.1; http://www.opensiteexplorer.org/dotbot, <EMAIL>)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; Linux x86_64; Mail.RU_Bot/2.0; +http://go.mail.ru/help/robots)", "browser": {}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/6.1; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; coccocbot-image/1.0; +http://help.coccoc.com/searchengine)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; MJ12bot/v1.4.8; http://mj12bot.com/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; SeznamBot/3.2; +http://napoveda.seznam.cz/en/seznambot-intro/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; Googlebot/2.1; startmebot/1.0; +https://start.me/bot)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (TweetmemeBot/4.0; +http://datasift.com/bot.html) Gecko/20100101 Firefox/31.0", "browser": {"name": "Firefox", "version": "31.0", "major": "31"}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; Go-http-client/1.1; +<EMAIL>)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; DotBot/1.1; http://www.opensiteexplorer.org/dotbot, <EMAIL>)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; SemrushBot/1.0~bm; +http://www.semrush.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; MJ12bot/v1.4.8; http://mj12bot.com/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; SemrushBot/3~bl; +http://www.semrush.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; Linux x86_64; Mail.RU_Bot/2.0; +http://go.mail.ru/help/robots)", "browser": {}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/6.1; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible;contxbot/1.0)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AmazonAdBot/1.0; +https://adbot.amazon.com)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Safari/537.36", "browser": {"name": "Safari", "version": "1", "major": "1"}, "cpu": {}, "device": {}, "engine": {"name": "WebKit", "version": "537.36"}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; SemrushBot/3~bl; +http://www.semrush.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; SemrushBot/1.0~bm; +http://www.semrush.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; DotBot/1.1; http://www.opensiteexplorer.org/dotbot, <EMAIL>)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/6.1; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; MJ12bot/v1.4.8; http://mj12bot.com/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; SeznamBot/3.2; +http://napoveda.seznam.cz/en/seznambot-intro/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; coccocbot-web/1.0; +http://help.coccoc.com/searchengine)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; SemrushBot/6~bl; +http://www.semrush.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; SemrushBot/6~bl; +http://www.semrush.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; DotBot/1.1; http://www.opensiteexplorer.org/dotbot, <EMAIL>)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; DotBot/1.1; http://www.opensiteexplorer.org/dotbot, <EMAIL>)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; DotBot/1.1; http://www.opensiteexplorer.org/dotbot, <EMAIL>)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; SemrushBot/6~bl; +http://www.semrush.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; SemrushBot/6~bl; +http://www.semrush.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/6.1; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/6.1; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/7.0; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/7.0; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/7.0; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/7.0; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/7.0; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/7.0; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.268", "browser": {"name": "Chrome", "version": "81.0.4044.268", "major": "81"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "81.0.4044.268"}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/7.0; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexRenderResourcesBot/1.0; +http://yandex.com/bots) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0", "browser": {"name": "Chrome", "version": "108.0.0.0", "major": "108"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "108.0.0.0"}, "os": {}}, {"ua": "AdsBot-Google", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm) Chrome/116.0.1938.76 Safari/537.36", "browser": {"name": "Chrome", "version": "116.0.1938.76", "major": "116"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "116.0.1938.76"}, "os": {}}, {"ua": "Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Chrome/124.0.6367.201 Safari/537.36", "browser": {"name": "Chrome", "version": "124.0.6367.201", "major": "124"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "124.0.6367.201"}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/7.0; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; GPTBot/1.2; +https://openai.com/gptbot)", "browser": {"name": "WebKit", "version": "537.36", "major": "537"}, "cpu": {}, "device": {}, "engine": {"name": "WebKit", "version": "537.36"}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexRenderResourcesBot/1.0; +http://yandex.com/bots) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0", "browser": {"name": "Chrome", "version": "108.0.0.0", "major": "108"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "108.0.0.0"}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexUserproxy; robot; +http://yandex.com/bots)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/7.0; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexRenderResourcesBot/1.0; +http://yandex.com/bots) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0", "browser": {"name": "Chrome", "version": "108.0.0.0", "major": "108"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "108.0.0.0"}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexRenderResourcesBot/1.0; +http://yandex.com/bots) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0", "browser": {"name": "Chrome", "version": "108.0.0.0", "major": "108"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "108.0.0.0"}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; BitSightBot/1.0)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Chrome/134.0.6998.165 Safari/537.36", "browser": {"name": "Chrome", "version": "134.0.6998.165", "major": "134"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "134.0.6998.165"}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; AhrefsBot/7.0; +http://ahrefs.com/robot/)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm) Chrome/116.0.1938.76 Safari/537.36", "browser": {"name": "Chrome", "version": "116.0.1938.76", "major": "116"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "116.0.1938.76"}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)", "browser": {}, "cpu": {}, "device": {}, "engine": {}, "os": {}}, {"ua": "Mozilla/5.0 (compatible; YandexRenderResourcesBot/1.0; +http://yandex.com/bots) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0", "browser": {"name": "Chrome", "version": "108.0.0.0", "major": "108"}, "cpu": {}, "device": {}, "engine": {"name": "Blink", "version": "108.0.0.0"}, "os": {}}]