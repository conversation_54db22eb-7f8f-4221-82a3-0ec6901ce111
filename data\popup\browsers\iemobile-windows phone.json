[{"ua": "Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 920)", "browser": {"name": "IEMobile", "version": "10.0", "major": "10"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 920", "vendor": "Nokia"}, "engine": {"name": "Trident", "version": "6.0"}, "os": {"name": "Windows Phone", "version": "8.0"}}, {"ua": "Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; Microsoft; Lumia 950)", "browser": {"name": "IEMobile", "version": "10.0", "major": "10"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 950", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "6.0"}, "os": {"name": "Windows Phone", "version": "8.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/8.0; Touch; WebView/2.0; rv:11.0; IEMobile/11.0; Microsoft; Lumia 650 Dual SIM) like Gecko", "browser": {"name": "IEMobile", "version": "11.0", "major": "11"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "8.0"}, "os": {"name": "Windows Phone", "version": "8.1"}}, {"ua": "Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/8.0; Touch; WebView/2.0; rv:11.0; IEMobile/11.0; Microsoft; Lumia 650) like Gecko", "browser": {"name": "IEMobile", "version": "11.0", "major": "11"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "8.0"}, "os": {"name": "Windows Phone", "version": "8.1"}}, {"ua": "Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/8.0; Touch; rv:11.0; IEMobile/11.0; Microsoft; Lumia 650 Dual SIM) like Gecko", "browser": {"name": "IEMobile", "version": "11.0", "major": "11"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "8.0"}, "os": {"name": "Windows Phone", "version": "8.1"}}, {"ua": "Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/8.0; Touch; rv:11.0; IEMobile/11.0; Microsoft; Lumia 650) like Gecko", "browser": {"name": "IEMobile", "version": "11.0", "major": "11"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "8.0"}, "os": {"name": "Windows Phone", "version": "8.1"}}, {"ua": "Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/8.0; Touch; rv:11.0; WebBrowser/8.1; IEMobile/11.0; Microsoft; Lumia 650 Dual SIM) like Gecko", "browser": {"name": "IEMobile", "version": "11.0", "major": "11"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "8.0"}, "os": {"name": "Windows Phone", "version": "8.1"}}, {"ua": "Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/8.0; Touch; rv:11.0; WebBrowser/8.1; IEMobile/11.0; Microsoft; Lumia 650 Dual SIM) like Gecko UCBrowser/4.2.1.541 Mobile", "browser": {"name": "IEMobile", "version": "11.0", "major": "11"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "8.0"}, "os": {"name": "Windows Phone", "version": "8.1"}}, {"ua": "Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/8.0; Touch; rv:11.0; WebBrowser/8.1; IEMobile/11.0; Microsoft; Lumia 650) like Gecko", "browser": {"name": "IEMobile", "version": "11.0", "major": "11"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "8.0"}, "os": {"name": "Windows Phone", "version": "8.1"}}, {"ua": "Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/8.0; Touch; rv:11.0; WebBrowser/8.1; IEMobile/11.0; Microsoft; Lumia 650) like Gecko MicroMessenger/6.0", "browser": {"name": "IEMobile", "version": "11.0", "major": "11"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "8.0"}, "os": {"name": "Windows Phone", "version": "8.1"}}, {"ua": "Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/8.0; Touch; rv:11.0; WebBrowser/8.1; IEMobile/11.0; Microsoft; Lumia 650) like Gecko UCBrowser/4.2.1.541 Mobile", "browser": {"name": "IEMobile", "version": "11.0", "major": "11"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "8.0"}, "os": {"name": "Windows Phone", "version": "8.1"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; ARM; Trident/8.0; Touch; rv:11.0; IEMobile/11.0; Microsoft; Lumia 650; Cortana 1.6.1.1032; 10.0.0.0.10586.21) like Gecko", "browser": {"name": "IEMobile", "version": "11.0", "major": "11"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "8.0"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; Microsoft; Lumia 650 Dual SIM)", "browser": {"name": "IEMobile", "version": "10.0", "major": "10"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "6.0"}, "os": {"name": "Windows Phone", "version": "8.0"}}, {"ua": "Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; Microsoft; Lumia 650)", "browser": {"name": "IEMobile", "version": "10.0", "major": "10"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "6.0"}, "os": {"name": "Windows Phone", "version": "8.0"}}, {"ua": "Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; Xbox; Xbox One)", "browser": {"name": "IEMobile", "version": "10.0", "major": "10"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "6.0"}, "os": {"name": "Windows Phone", "version": "8.0"}}, {"ua": "Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 920)", "browser": {"name": "IEMobile", "version": "10.0", "major": "10"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 920", "vendor": "Nokia"}, "engine": {"name": "Trident", "version": "6.0"}, "os": {"name": "Windows Phone", "version": "8.0"}}, {"ua": "Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 920)", "browser": {"name": "IEMobile", "version": "10.0", "major": "10"}, "cpu": {"architecture": "arm"}, "device": {"type": "mobile", "model": "Lumia 920", "vendor": "Nokia"}, "engine": {"name": "Trident", "version": "6.0"}, "os": {"name": "Windows Phone", "version": "8.0"}}]