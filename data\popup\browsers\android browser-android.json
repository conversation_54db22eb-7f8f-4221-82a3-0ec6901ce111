[{"ua": "Mozilla/5.0 (Linux; U; Android 4.4.2; en-us; SCH-I535 Build/KOT49H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "SCH-I535", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; ko-kr; LG-L160L Build/IML74K) AppleWebkit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "L160L", "vendor": "LG"}, "engine": {"name": "Webkit", "version": "534.30"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-ch; HTC Sensation Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Sensation", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.5; zh-cn; HTC_IncredibleS_S710e Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "IncredibleS S710e", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.5"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.5; en-us; HTC Vision Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Vision", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.5"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.4; fr-fr; HTC Desire Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Desire", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.4"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.4; en-us; T-Mobile myTouch 3G Slide Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "T-Mobile myTouch 3G Slide"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.4"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.3; zh-tw; HTC_Pyramid Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Pyramid", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.3; zh-tw; HTC_Pyramid Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Pyramid", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.3; zh-tw; HTC Pyramid Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Pyramid", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.3; ko-kr; LG-LU3000 Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "LU3000", "vendor": "LG"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.3; en-us; HTC_DesireS_S510e Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "DesireS S510e", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.3; de-de; HTC Desire Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Desire", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.3; de-ch; HTC Desire Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Desire", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.2; fr-lu; HTC Legend Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Legend", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.2; en-sa; HTC_DesireHD_A9191 Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "DesireHD A9191", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.2.1; fr-fr; HTC_DesireZ_A7272 Build/FRG83D) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "DesireZ A7272", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.2.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.2.1; en-gb; HTC_DesireZ_A7272 Build/FRG83D) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "DesireZ A7272", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.2.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.2.1; en-ca; LG-P505R Build/FRG83) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "P505R", "vendor": "LG"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.2.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.2) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.3; de-de; GT-I9300 Build/JSS15J) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I9300", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.2.1; en-us; Nexus One Build/FRG83) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus One"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.2.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.3; en-us; SM-N900T Build/JSS15J) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N900T", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.1.2; de-de; GT-I8190 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I8190", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; de-de; SAMSUNG GT-I9195 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/1.5 Chrome/28.0.1500.94 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "1.5", "major": "1"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I9195", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "28.0.1500.94"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; BroadSign Xpress 1.0.15-6 B- (720) Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "BroadSign Xpress 1.0.15-6 B- (720)"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.4; DL Build/DL3188) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "DL"}, "engine": {"name": "Blink", "version": "33.0.0.0"}, "os": {"name": "Android", "version": "4.4.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; de-de; SAMSUNG GT-I9301I Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/1.5 Chrome/28.0.1500.94 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "1.5", "major": "1"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I9301I", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "28.0.1500.94"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.2; en-us; SCH-I800 Build/FROYO) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 DMBrowser-BV", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "SCH-I800", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.2; de-de; GT-P5210 Build/KOT49H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "GT-P5210", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.1.2; de-de; ME371MG Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "ME371MG"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4; Nexus 5 Build/LMY48B ) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 5", "vendor": "LG"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.1.2; de-de; GT-I9100 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I9100", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; de-de; SAMSUNG GT-I9505 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/1.5 Chrome/28.0.1500.94 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "1.5", "major": "1"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I9505", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "28.0.1500.94"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; SM-T110 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T110", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.2; pt-br; LG-D337 Build/KOT49I) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/34.0.1847.118 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "D337", "vendor": "LG"}, "engine": {"name": "Blink", "version": "34.0.1847.118"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.2; es-es; SM-T210R Build/KOT49H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T210R", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; GT-P5110 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "GT-P5110", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; GT-I8200N Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I8200N", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0(Linux;Android 5.1.1;OPPO A33 Build/LMY47V;wv) AppleWebKit/537.36(KHTML,link Gecko) Version/4.0 Chrome/42.0.2311.138 Mobile Safari/537.36 Mb2345Browser/9.0", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "A33", "vendor": "OPPO"}, "engine": {"name": "Blink", "version": "42.0.2311.138"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2.2; pl-pl; GT-P5110 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "GT-P5110", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0(Linux;Android 5.1.1;OPPO A33 Build/LMY47V;wv) AppleWebKit/537.36(KHTML,link Gecko) Version/4.0 Chrome/43.0.2357.121 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "A33", "vendor": "OPPO"}, "engine": {"name": "Blink", "version": "43.0.2357.121"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.2; de-de; GT-P5200 Build/KOT49H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "GT-P5200", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.4; en-us; Kindle Fire Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Kindle Fire"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.4"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.4; en-us; Kindle Fire Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 cordova-amazon-fireos/3.4.0 AmazonWebAppPlatform/3.4.0;1.0", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Kindle Fire"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.4"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.3; zh-tw; HTC_Pyramid Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Pyramid", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2; xx-xx; GT-I9500 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I9500", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2; xx-xx; GT-I9500 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I9500", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.4; en-us; Kindle Fire Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 cordova-amazon-fireos/3.4.0 AmazonWebAppPlatform/3.4.0;1.0", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Kindle Fire"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; TESLA Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "TESLA"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36 (Mobile; afma-sdk-a-v201004999.12211000.1)", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36 (Mobile; afma-sdk-a-v201004999.19649000.1)", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36 (Mobile; afma-sdk-a-v201604999.12451000.1)", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36 (Mobile; afma-sdk-a-v202006999.201604000.1)", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36 YandexSearch/6.10/apad", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36 YandexSearch/6.30/apad", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36 YandexSearch/7.15/apad", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36 YandexSearch/7.16/apad", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/Tesla_2014.09.12) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Build/Tesla_2014.12.29) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla Evo 5.0 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla Evo 5.0"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Tesla TTL7 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla TTL7"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; TeslaEvo5.0 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "TeslaEvo5.0"}, "engine": {"name": "Blink", "version": "30.0.0.0"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.4; TESLA Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "TESLA"}, "engine": {"name": "Blink", "version": "33.0.0.0"}, "os": {"name": "Android", "version": "4.4.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.4; Tesla Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "33.0.0.0"}, "os": {"name": "Android", "version": "4.4.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.4; Tesla Build/KTU84Q) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "33.0.0.0"}, "os": {"name": "Android", "version": "4.4.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.4; Tesla L7 Quad Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla L7 Quad"}, "engine": {"name": "Blink", "version": "33.0.0.0"}, "os": {"name": "Android", "version": "4.4.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; LP_TESLA Build/LMY47I) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/39.0.0.0 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "LP_TESLA"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/39.0.0.0 Safari/537.36 (Mobile; afma-sdk-a-v20088999.13000000.1)", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/39.0.0.0 Safari/537.36 YandexSearch/7.63/apad YandexSearchBrowser/7.63", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/39.0.0.0 Safari/537.36 YandexSearch/8.30/apad YandexSearchBrowser/8.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/39.0.0.0 Safari/537.36 com.yandex.zen/4.5.1.2323 (<PERSON><PERSON> Tesla; Android 5.1) ZenKit/1.40.5.1-internalNewdesign-Zen.7853", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "Blink", "version": "39.0.0.0"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; TESLA Build/AD198_D_BP331_50) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2531.0 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "TESLA"}, "engine": {"name": "Blink", "version": "48.0.2531.0"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Pixel C Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/52.0.2743.98 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "52.0.2743.98"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1 Build/NRD90M; w; Krstarica.app) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/84.0.4147.125 Mobile Safari/537.3", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM7.181205.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/78.0.3904.96 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM7.181205.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.136 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "79.0.3945.136"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM7.181205.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Safari/537.36 GNews/2021022310", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190105.002) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Safari/537.36 GNews/2021022310", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.101 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/85.0.4183.127 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.75 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.75"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.99 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.110 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.185 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.66 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.101 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.141 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.72 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.86 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.86 Safari/537.36 GNews/2021022310", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Safari/537.36 GNews/2021022310", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.105 Safari/537.36 GNews/2021022310", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190605.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.66 Safari/537.36 GNews/2021022310", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "90.0.4430.66"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Pixel C Build/PQ3A.190605.003) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C Build/QQ1B.200105.004) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.186 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "74.0.3729.186"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C Build/QQ2A.200305.003) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.117 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.117"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C Build/QQ2A.200501.001.B2) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/81.0.4044.117 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "81.0.4044.117"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C Build/QQ3A.200805.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.198 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C Build/RQ1A.201205.008) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C Build/RQ1A.210105.003) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C Build/RQ1A.210205.004) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.90 Safari/537.36 GNews/2021022310", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel C Build/RQ2A.210305.006) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.152 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; TESLA Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "TESLA"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2.2; ru-ru; TESLA Gravity 9.7 SHD Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "TESLA Gravity 9.7 SHD"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2.2; ru-ru; Tesla 7.0 3G Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla 7.0 3G"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2.2; ru-ru; Tesla 9.7 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla 9.7"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2.2; ru-ru; Tesla Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2.2; ru-ru; Tesla Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/E7FBAF", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2.2; ru-ru; Tesla Build/Tesla) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.2; fr-fr; Vertis Braver Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.2; ru-ru; Tesla Build/KTU84Q) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 6.0.1; en-us; TESLA Build/JOP24G) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Chrome/43.0.2357.65 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "TESLA"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; Kindle Fire HDX Build/NJH47D) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/59.0.3071.92 Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Kindle Fire HDX"}, "engine": {"name": "Blink", "version": "59.0.3071.92"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; de-de; SAMSUNG GT-I9195 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/1.5 Chrome/28.0.1500.94 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "1.5", "major": "1"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I9195", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "28.0.1500.94"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.5; en-us; HTC Vision Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Vision", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.5"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.3; de-ch; HTC Desire Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Desire", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-ch; HTC Sensation Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "Sensation", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 2.3.3; en-us; HTC_DesireS_S510e Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "DesireS S510e", "vendor": "HTC"}, "engine": {"name": "WebKit", "version": "533.1"}, "os": {"name": "Android", "version": "2.3.3"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.4.2; ru-ru; Tesla Build/KTU84Q) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "tablet", "model": "Tesla"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 6.0.1; en-us; TESLA Build/JOP24G) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Chrome/43.0.2357.65 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "TESLA"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 14; M2101K6G Build/AP2A.240805.005) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/128.0.6613.88 Mobile Safari/537.36", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "M2101K6G", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "128.0.6613.88"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; U; Android 4.3; en-us; SM-N900T Build/JSS15J) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "browser": {"name": "Android Browser", "version": "4.0", "major": "4"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N900T", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "534.30"}, "os": {"name": "Android", "version": "4.3"}}]