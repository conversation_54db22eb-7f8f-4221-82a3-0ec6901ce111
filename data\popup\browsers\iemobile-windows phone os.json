[{"ua": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0)", "browser": {"name": "IEMobile", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile"}, "engine": {"name": "Trident", "version": "5.0"}, "os": {"name": "Windows Phone OS", "version": "7.5"}}, {"ua": "Mozilla/4.0 (compatible; MSIE 7.0; Windows Phone OS 7.0; Trident/3.1; IEMobile/7.0; Nokia;N70)", "browser": {"name": "IEMobile", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "vendor": "Nokia"}, "engine": {"name": "Trident", "version": "3.1"}, "os": {"name": "Windows Phone OS", "version": "7.0"}}, {"ua": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Microsoft; Lumia 650 Dual SIM)", "browser": {"name": "IEMobile", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "5.0"}, "os": {"name": "Windows Phone OS", "version": "7.5"}}, {"ua": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Microsoft; Lumia 650)", "browser": {"name": "IEMobile", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "5.0"}, "os": {"name": "Windows Phone OS", "version": "7.5"}}, {"ua": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Xbox)", "browser": {"name": "IEMobile", "version": "9.0", "major": "9"}, "cpu": {}, "device": {"type": "console", "model": "Xbox", "vendor": "Microsoft"}, "engine": {"name": "Trident", "version": "5.0"}, "os": {"name": "Windows Phone OS", "version": "7.5"}}, {"ua": "Mozilla/4.0 (compatible; MSIE 7.0; Windows Phone OS 7.0; Trident/3.1; IEMobile/7.0) Asus;Galaxy6", "browser": {"name": "IEMobile", "version": "7.0", "major": "7"}, "cpu": {}, "device": {"type": "mobile", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Trident", "version": "3.1"}, "os": {"name": "Windows Phone OS", "version": "7.0"}}]