[{"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 950) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14977", "browser": {"name": "Edge", "version": "15.14977", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 950", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14977"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; RM-1067) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063", "browser": {"name": "Edge", "version": "15.15063", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "RM-1067"}, "engine": {"name": "EdgeHTML", "version": "15.15063"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; RM-1067) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063", "browser": {"name": "Edge", "version": "15.15063", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "RM-1067"}, "engine": {"name": "EdgeHTML", "version": "15.15063"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; MSAppHost/3.0; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586", "browser": {"name": "Edge", "version": "13.10586", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "13.10586"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586", "browser": {"name": "Edge", "version": "13.10586", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "13.10586"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586", "browser": {"name": "Edge", "version": "13.10586", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "13.10586"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263", "browser": {"name": "Edge", "version": "14.14263", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14263"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.82 Mobile Safari/537.36 Edge/14.14328", "browser": {"name": "Edge", "version": "14.14328", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14328"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; WebView/3.0; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586", "browser": {"name": "Edge", "version": "13.10586", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "13.10586"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; WebView/3.0; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586", "browser": {"name": "Edge", "version": "13.10586", "major": "13"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "13.10586"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2251.1 Mobile Safari/537.36 Edge/13.10586", "browser": {"name": "Edge", "version": "13.10586", "major": "13"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "13.10586"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10575", "browser": {"name": "Edge", "version": "13.10575", "major": "13"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "13.10575"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10577", "browser": {"name": "Edge", "version": "13.10577", "major": "13"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "13.10577"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586", "browser": {"name": "Edge", "version": "13.10586", "major": "13"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "13.10586"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586(Linux LLC 1.2)", "browser": {"name": "Edge", "version": "13.10586", "major": "13"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "13.10586"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; MSAppHost/3.0; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14393", "browser": {"name": "Edge", "version": "14.14393", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14393"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; MSAppHost/3.0; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063", "browser": {"name": "Edge", "version": "15.15063", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15063"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; MSAppHost/3.0; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; MSAppHost/3.0; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14393", "browser": {"name": "Edge", "version": "14.14393", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14393"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; MSAppHost/3.0; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063", "browser": {"name": "Edge", "version": "15.15063", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15063"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; MSAppHost/3.0; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.82 Mobile Safari/537.36 Edge/14.14361", "browser": {"name": "Edge", "version": "14.14361", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14361"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.82 Mobile Safari/537.36 Edge/14.14367", "browser": {"name": "Edge", "version": "14.14367", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14367"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.82 Mobile Safari/537.36 Edge/14.14372", "browser": {"name": "Edge", "version": "14.14372", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14372"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14388", "browser": {"name": "Edge", "version": "14.14388", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14388"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14393", "browser": {"name": "Edge", "version": "14.14393", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14393"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14905", "browser": {"name": "Edge", "version": "14.14905", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14905"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14936", "browser": {"name": "Edge", "version": "14.14936", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14936"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14946", "browser": {"name": "Edge", "version": "15.14946", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14946"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14951", "browser": {"name": "Edge", "version": "15.14951", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14951"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14955", "browser": {"name": "Edge", "version": "15.14955", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14955"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14965", "browser": {"name": "Edge", "version": "15.14965", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14965"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14998", "browser": {"name": "Edge", "version": "15.14998", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14998"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15007", "browser": {"name": "Edge", "version": "15.15007", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15007"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15014", "browser": {"name": "Edge", "version": "15.15014", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15014"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15025", "browser": {"name": "Edge", "version": "15.15025", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15025"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15043", "browser": {"name": "Edge", "version": "15.15043", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15043"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15051", "browser": {"name": "Edge", "version": "15.15051", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15051"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15055", "browser": {"name": "Edge", "version": "15.15055", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15055"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063", "browser": {"name": "Edge", "version": "15.15063", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15063"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15204", "browser": {"name": "Edge", "version": "15.15204", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15204"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15205", "browser": {"name": "Edge", "version": "15.15205", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15205"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15223", "browser": {"name": "Edge", "version": "15.15223", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15223"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15226", "browser": {"name": "Edge", "version": "15.15226", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15226"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15228", "browser": {"name": "Edge", "version": "15.15228", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15228"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15230", "browser": {"name": "Edge", "version": "15.15230", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15230"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15235", "browser": {"name": "Edge", "version": "15.15235", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15235"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15237", "browser": {"name": "Edge", "version": "15.15237", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15237"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15240", "browser": {"name": "Edge", "version": "15.15240", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15240"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15245", "browser": {"name": "Edge", "version": "15.15245", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15245"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15252", "browser": {"name": "Edge", "version": "15.15252", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15252"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM)AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650 Dual SIM; Cortana 1.8.12.15254; 10.0.0.0.15254.603) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.82 Mobile Safari/537.36 Edge/14.14367", "browser": {"name": "Edge", "version": "14.14367", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14367"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14383", "browser": {"name": "Edge", "version": "14.14383", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14383"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14388", "browser": {"name": "Edge", "version": "14.14388", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14388"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14390", "browser": {"name": "Edge", "version": "14.14390", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14390"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14393", "browser": {"name": "Edge", "version": "14.14393", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14393"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14393:", "browser": {"name": "Edge", "version": "14.14393", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14393"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14393;", "browser": {"name": "Edge", "version": "14.14393", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14393"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14905", "browser": {"name": "Edge", "version": "14.14905", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14905"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14915", "browser": {"name": "Edge", "version": "14.14915", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14915"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14936", "browser": {"name": "Edge", "version": "14.14936", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14936"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14900", "browser": {"name": "Edge", "version": "15.14900", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14900"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14946", "browser": {"name": "Edge", "version": "15.14946", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14946"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14951", "browser": {"name": "Edge", "version": "15.14951", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14951"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14955", "browser": {"name": "Edge", "version": "15.14955", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14955"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14959", "browser": {"name": "Edge", "version": "15.14959", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14959"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14965", "browser": {"name": "Edge", "version": "15.14965", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14965"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15007", "browser": {"name": "Edge", "version": "15.15007", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15007"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15014", "browser": {"name": "Edge", "version": "15.15014", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15014"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15031", "browser": {"name": "Edge", "version": "15.15031", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15031"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15043", "browser": {"name": "Edge", "version": "15.15043", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15043"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15047", "browser": {"name": "Edge", "version": "15.15047", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15047"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15051", "browser": {"name": "Edge", "version": "15.15051", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15051"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15055", "browser": {"name": "Edge", "version": "15.15055", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15055"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063", "browser": {"name": "Edge", "version": "15.15063", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15063"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063;", "browser": {"name": "Edge", "version": "15.15063", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15063"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15204", "browser": {"name": "Edge", "version": "15.15204", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15204"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15205", "browser": {"name": "Edge", "version": "15.15205", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15205"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15208", "browser": {"name": "Edge", "version": "15.15208", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15208"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15210", "browser": {"name": "Edge", "version": "15.15210", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15210"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15213", "browser": {"name": "Edge", "version": "15.15213", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15213"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15215", "browser": {"name": "Edge", "version": "15.15215", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15215"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15222", "browser": {"name": "Edge", "version": "15.15222", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15222"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15223", "browser": {"name": "Edge", "version": "15.15223", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15223"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15228", "browser": {"name": "Edge", "version": "15.15228", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15228"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15230", "browser": {"name": "Edge", "version": "15.15230", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15230"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15235", "browser": {"name": "Edge", "version": "15.15235", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15235"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15240", "browser": {"name": "Edge", "version": "15.15240", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15240"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15245", "browser": {"name": "Edge", "version": "15.15245", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15245"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15250", "browser": {"name": "Edge", "version": "15.15250", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15250"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15252", "browser": {"name": "Edge", "version": "15.15252", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15252"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254 XING-Windows-Universal/4.0.5.0/Windows.Mobile.10.0.15254.603 ttt_webview_w10m", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/21CDD77C", "browser": {"name": "Edge", "version": "21CDD77C", "major": "2177"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "21CDD77C"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 MobileSafari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Mobile Safari/537.36 Edge/15.14900", "browser": {"name": "Edge", "version": "15.14900", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14900"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650; Cortana 1.8.12.15254; 10.0.0.0.15254.603) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft;Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063", "browser": {"name": "Edge", "version": "15.15063", "major": "15"}, "cpu": {}, "device": {"type": "mobile"}, "engine": {"name": "EdgeHTML", "version": "15.15063"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft;Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; MicrosoftMDG; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; MicrosoftMDG; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; WebView/3.0; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14388", "browser": {"name": "Edge", "version": "14.14388", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14388"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; WebView/3.0; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14393", "browser": {"name": "Edge", "version": "14.14393", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14393"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; WebView/3.0; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063", "browser": {"name": "Edge", "version": "15.15063", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15063"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; WebView/3.0; Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; WebView/3.0; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14393", "browser": {"name": "Edge", "version": "14.14393", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14393"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; WebView/3.0; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14915", "browser": {"name": "Edge", "version": "14.14915", "major": "14"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14915"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; WebView/3.0; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.14965", "browser": {"name": "Edge", "version": "15.14965", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.14965"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; WebView/3.0; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15025", "browser": {"name": "Edge", "version": "15.15025", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15025"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; WebView/3.0; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063", "browser": {"name": "Edge", "version": "15.15063", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15063"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; WebView/3.0; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; WebView/3.0; MicrosoftMDG; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.82 Mobile Safari/537.36 Edge/14.14352", "browser": {"name": "Edge", "version": "14.14352", "major": "14"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14352"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.82 Mobile Safari/537.36 Edge/14.14366", "browser": {"name": "Edge", "version": "14.14366", "major": "14"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14366"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14379", "browser": {"name": "Edge", "version": "14.14379", "major": "14"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14379"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14383", "browser": {"name": "Edge", "version": "14.14383", "major": "14"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14383"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14385", "browser": {"name": "Edge", "version": "14.14385", "major": "14"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14385"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14388", "browser": {"name": "Edge", "version": "14.14388", "major": "14"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14388"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14389", "browser": {"name": "Edge", "version": "14.14389", "major": "14"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14389"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14391", "browser": {"name": "Edge", "version": "14.14391", "major": "14"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14391"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Mobile Safari/537.36 Edge/14.14393", "browser": {"name": "Edge", "version": "14.14393", "major": "14"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "14.14393"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15023", "browser": {"name": "Edge", "version": "15.15023", "major": "15"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15023"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15026", "browser": {"name": "Edge", "version": "15.15026", "major": "15"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15026"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15039", "browser": {"name": "Edge", "version": "15.15039", "major": "15"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15039"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15045", "browser": {"name": "Edge", "version": "15.15045", "major": "15"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15045"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063", "browser": {"name": "Edge", "version": "15.15063", "major": "15"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15063"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063;", "browser": {"name": "Edge", "version": "15.15063", "major": "15"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15063"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Mobile Safari/537.36 Edge/16.16257", "browser": {"name": "Edge", "version": "16.16257", "major": "16"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "16.16257"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Mobile Safari/537.36 Edge/16.16262", "browser": {"name": "Edge", "version": "16.16262", "major": "16"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "16.16262"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Mobile Safari/537.36 Edge/16.16281", "browser": {"name": "Edge", "version": "16.16281", "major": "16"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "16.16281"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Mobile Safari/537.36 Edge/16.16287", "browser": {"name": "Edge", "version": "16.16287", "major": "16"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "16.16287"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Mobile Safari/537.36 Edge/16.16293", "browser": {"name": "Edge", "version": "16.16293", "major": "16"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "16.16293"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Mobile Safari/537.36 Edge/16.16295", "browser": {"name": "Edge", "version": "16.16295", "major": "16"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "16.16295"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Mobile Safari/537.36 Edge/16.16297", "browser": {"name": "Edge", "version": "16.16297", "major": "16"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "16.16297"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Mobile Safari/537.36 Edge/16.16299", "browser": {"name": "Edge", "version": "16.16299", "major": "16"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "16.16299"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Mobile Safari/537.36 Mobile Edge/42.0.0.2028", "browser": {"name": "Edge", "version": "42.0.0.2028", "major": "42"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "42.0.0.2028"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1;Microsoft; Lumia 650 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650 Dual SIM", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0 (Windows Phone 10.0; Android 10; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36 Edge/16.16299", "browser": {"name": "Edge", "version": "16.16299", "major": "16"}, "cpu": {}, "device": {"type": "console", "model": "Xbox One", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "16.16299"}, "os": {"name": "Windows Phone", "version": "10.0"}}, {"ua": "Mozilla/5.0(Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15254", "browser": {"name": "Edge", "version": "15.15254", "major": "15"}, "cpu": {}, "device": {"type": "mobile", "model": "Lumia 650", "vendor": "Microsoft"}, "engine": {"name": "EdgeHTML", "version": "15.15254"}, "os": {"name": "Windows Phone", "version": "10.0"}}]