[{"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-G920F Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.84 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "62.0.3202.84", "major": "62"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G920F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "62.0.3202.84"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; vivo 1603 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "58.0.3029.83", "major": "58"}, "cpu": {}, "device": {"type": "mobile", "model": "1603", "vendor": "Vivo"}, "engine": {"name": "Blink", "version": "58.0.3029.83"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; POCOPHONE F1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.136 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "74.0.3729.136", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "POCOPHONE F1", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "74.0.3729.136"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "65.0.3325.181", "major": "65"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 5", "vendor": "LG"}, "engine": {"name": "Blink", "version": "65.0.3325.181"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; vivo 1606 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.124 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "53.0.2785.124", "major": "53"}, "cpu": {}, "device": {"type": "mobile", "model": "1606", "vendor": "Vivo"}, "engine": {"name": "Blink", "version": "53.0.2785.124"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-G610F Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "63.0.3239.111", "major": "63"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G610F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "63.0.3239.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; FIG-LX3 Build/HUAWEIFIG-LX3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "mobile", "model": "FIG-LX3", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-G610M Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "67.0.3396.87", "major": "67"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G610M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-G610M Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.109 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "65.0.3325.109", "major": "65"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G610M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "65.0.3325.109"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Moto G (4)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.80 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "70.0.3538.80", "major": "70"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (4)", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "70.0.3538.80"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Moto G (5S) Plus Build/NPSS26.116-64-11) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.91 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "68.0.3440.91", "major": "68"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (5S) Plus", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "68.0.3440.91"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Moto G (4) Build/NPJS25.93-14-18) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.91 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "68.0.3440.91", "major": "68"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (4)", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "68.0.3440.91"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; FIG-LX3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.80 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "70.0.3538.80", "major": "70"}, "cpu": {}, "device": {"type": "mobile", "model": "FIG-LX3", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "70.0.3538.80"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Moto E (4) Plus) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.80 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "70.0.3538.80", "major": "70"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto E (4) Plus", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "70.0.3538.80"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-J710MN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.80 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "70.0.3538.80", "major": "70"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J710MN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "70.0.3538.80"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; U; Android-4.0.3; en-us; Xoom Build/IML77) AppleWebKit/535.7 (KHTML, like Gecko) CrMo/16.0.912.75 Safari/535.7", "browser": {"name": "Mobile Chrome", "version": "16.0.912.75", "major": "16"}, "cpu": {}, "device": {"type": "tablet", "model": "Xoom", "vendor": "Motorola"}, "engine": {"name": "WebKit", "version": "535.7"}, "os": {"name": "Android", "version": "4.0.3"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; MYA-L03 Build/HUAWEIMYA-L03) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "mobile", "model": "MYA-L03", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G965U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "78.0.3904.108", "major": "78"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G930F Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; HUAWEI VNS-L23 Build/HUAWEIVNS-L23) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "mobile", "model": "VNS-L23", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Moto G (5) Plus Build/NPNS25.137-92-14) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "67.0.3396.87", "major": "67"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (5) Plus", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Moto C Build/NRD90M.063) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto C", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G930F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.80 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "70.0.3538.80", "major": "70"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "70.0.3538.80"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; LG-Q710AL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "78.0.3904.108", "major": "78"}, "cpu": {}, "device": {"type": "mobile", "model": "Q710AL", "vendor": "LG"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-J700M Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.137 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "64.0.3282.137", "major": "64"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J700M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "64.0.3282.137"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-G532M Build/MMB29T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "67.0.3396.87", "major": "67"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G532M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "67.0.3396.87"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; vivo 1603 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "58.0.3029.83", "major": "58"}, "cpu": {}, "device": {"type": "mobile", "model": "1603", "vendor": "Vivo"}, "engine": {"name": "Blink", "version": "58.0.3029.83"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G975U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.93 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "79.0.3945.93", "major": "79"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "79.0.3945.93"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; SM-N971N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N971N", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Redmi 4X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.125 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 4X", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G570Y) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G570Y", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 5 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 5 Pro", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "90.0.4430.91", "major": "90"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "90.0.4430.91"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; GT-I9301I) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "GT-I9301I", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.4; TESLA) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "TESLA"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "4.4.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0; Pixel C Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.122 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.122", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.122"}, "os": {"name": "Android", "version": "5.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1.1; SM-J320F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.88 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.88", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J320F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.88"}, "os": {"name": "Android", "version": "5.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla_SP9 Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "56.0.2924.87", "major": "56"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9"}, "engine": {"name": "Blink", "version": "56.0.2924.87"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla_SP9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "79.0.3945.79", "major": "79"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9"}, "engine": {"name": "Blink", "version": "79.0.3945.79"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.1; Tesla_SP9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.81 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "5.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Pixel C Build/M5C14J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2526.83 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "47.0.2526.83", "major": "47"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "47.0.2526.83"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Redmi 3S) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 3S", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-G900F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G900F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; SM-N910C) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.185 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N910C", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; STH100-2) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.90 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "STH100-2", "vendor": "BlackBerry"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; BG2-W09) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "BG2-W09"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; TESLA) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.99 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.99", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "TESLA"}, "engine": {"name": "Blink", "version": "80.0.3987.99"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0; Tesla) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "78.0.3904.96", "major": "78"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "6.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; 9008D) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "9008D"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; EVA-L09) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "EVA-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; HUAWEI VNS-L31) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": " VNS-L31", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; LG-M210) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/71.0.3578.83 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "71.0.3578.83", "major": "71"}, "cpu": {}, "device": {"type": "mobile", "model": "M210", "vendor": "LG"}, "engine": {"name": "Blink", "version": "71.0.3578.83"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; NEM-L51) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "NEM-L51", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Redmi Note 4) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 4", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-G920F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G920F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; SM-J327T1) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J327T1", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla L7.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.99 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.99", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla L7.1"}, "engine": {"name": "Blink", "version": "80.0.3987.99"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla L7.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.110 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla L7.1"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Lite Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.98 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "61.0.3163.98", "major": "61"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "61.0.3163.98"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.75 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "73.0.3683.75", "major": "73"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "73.0.3683.75"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.73 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "77.0.3865.73", "major": "77"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "77.0.3865.73"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.116 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "79.0.3945.116", "major": "79"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "79.0.3945.116"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.117 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "81.0.4044.117", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "81.0.4044.117"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.81 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "85.0.4183.81", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "85.0.4183.81"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3 Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3 Lite"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.116 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "77.0.3865.116", "major": "77"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.162", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3"}, "engine": {"name": "Blink", "version": "80.0.3987.162"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.111 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "84.0.4147.111", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3"}, "engine": {"name": "Blink", "version": "84.0.4147.111"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP3.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3.3"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.91 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "69.0.3497.91", "major": "69"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "69.0.3497.91"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "75.0.3770.101", "major": "75"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "75.0.3770.101"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "78.0.3904.96", "major": "78"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "78.0.3904.96"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.117 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.117", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "80.0.3987.117"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.125 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.185 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP6.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.181 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.181", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6.3"}, "engine": {"name": "Blink", "version": "88.0.4324.181"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "74.0.3729.157", "major": "74"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "74.0.3729.157"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "78.0.3904.108", "major": "78"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "85.0.4183.101", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "85.0.4183.101"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.116 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "77.0.3865.116", "major": "77"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1L"}, "engine": {"name": "Blink", "version": "77.0.3865.116"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.149", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1L"}, "engine": {"name": "Blink", "version": "80.0.3987.149"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1L"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.75", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1L"}, "engine": {"name": "Blink", "version": "86.0.4240.75"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Tesla_SP9.1L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.141 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.141", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9.1L"}, "engine": {"name": "Blink", "version": "88.0.4324.141"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; XT1580) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "XT1580", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Moto E (4) Plus) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto E (4) Plus", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.182 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.182", "major": "88"}, "cpu": {}, "device": {"type": "mobile"}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1; Pixel C Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.163 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.163", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.163"}, "os": {"name": "Android", "version": "7.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; F5321) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.90 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "F5321", "vendor": "Sony"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; LLD-L31) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "LLD-L31", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; S41) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "S41"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-A320FL) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A320FL", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-A520F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.111 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.111", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A520F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-A530F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.105 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A530F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G930F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G930F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G930F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; SM-G935F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G935F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; WAS-LX1A) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "WAS-LX1A", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0; Pixel C Build/OPR5.170623.014) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.122 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.122", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.122"}, "os": {"name": "Android", "version": "8.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; DRA-L21) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.105 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "DRA-L21", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; LM-X210APM) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-X210APM", "vendor": "LG"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Moto G (5)) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.105 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (5)", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; SM-J727U) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J727U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.87 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.87", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "80.0.3987.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "83.0.4103.96", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "83.0.4103.96"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.110 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4 Build/O11019) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.132 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "76.0.3809.132", "major": "76"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "76.0.3809.132"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.87 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.87", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "80.0.3987.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.117 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "81.0.4044.117", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "81.0.4044.117"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.125 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.114", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "86.0.4240.114"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP3_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.141 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.141", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP3_4"}, "engine": {"name": "Blink", "version": "88.0.4324.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite Build/O11019) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.93 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.93", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "88.0.4324.93"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.87 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.87", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "80.0.3987.87"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.125 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP6_4_Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP6_4_Lite"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2 Build/O11019) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.111 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "76.0.3809.111", "major": "76"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "76.0.3809.111"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "78.0.3904.108", "major": "78"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "78.0.3904.108"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.162", "major": "80"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "80.0.3987.162"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "83.0.4103.101", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "83.0.4103.101"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.120 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "85.0.4183.120", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "85.0.4183.120"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; Tesla_SP9_2) AppleWebKit/537.36 (KHTML; like Gecko) Chrome/86.0.4240.110 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.110", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Tesla_SP9_2"}, "engine": {"name": "Blink", "version": "86.0.4240.110"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; ZTE Blade A0722) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Blade A0722", "vendor": "ZTE"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9.0; Pixel C Build/PQ3B.190705.003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.70 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "78.0.3904.70", "major": "78"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "78.0.3904.70"}, "os": {"name": "Android", "version": "9.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ANE-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.111 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.111", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "ANE-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ANE-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.90 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "ANE-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; BND-L21) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "BND-L21", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; FIG-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "FIG-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; H3113) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "H3113"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; HTC U12+) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "U12", "vendor": "HTC"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; LG-H870) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "H870", "vendor": "LG"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Lenovo PB-6505M) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "PB-6505M", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Mi 9T Pro) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi 9T Pro"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ONEPLUS A3003) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/84.0.4147.125 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "A3003", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ONEPLUS A5000) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "A5000", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Redmi 7) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.88 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.88", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 7", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.88"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Redmi Note 7) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/83.0.4103.106 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "83.0.4103.106", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 7", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "83.0.4103.106"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Redmi Note 8) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 8", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-A405FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.90 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A405FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G390F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G390F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G950F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.99 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.99", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.99"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G950F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G950F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G955F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G955F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G970F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-J415FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.111 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.111", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J415FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-N950F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N950F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; STF-L09) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "STF-L09", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Mi A2) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.90 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi A2", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; NOH-NX9) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "NOH-NX9", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Nokia 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "6.1", "vendor": "Nokia"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Nokia 7.2) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "7.2", "vendor": "Nokia"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; ONEPLUS A5010) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "A5010", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; ONEPLUS A6003) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/84.0.4147.125 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "A6003", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; ONEPLUS A6003) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "A6003", "vendor": "OnePlus"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; PCT-L29) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "PCT-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; POT-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "POT-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel 3) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/83.0.4103.116 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "83.0.4103.116", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3", "vendor": "Google"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Pixel C Build/H9436) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.163 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "80.0.3987.163", "major": "80"}, "cpu": {}, "device": {"type": "tablet", "model": "Pixel C", "vendor": "Google"}, "engine": {"name": "Blink", "version": "80.0.3987.163"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi 7) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 7", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 7) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 7", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 8 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/83.0.4103.116 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "83.0.4103.116", "major": "83"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 8 Pro", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "83.0.4103.116"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 8 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.88 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.88", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 8 Pro", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.88"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 8 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 8 Pro", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 8 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 8 Pro", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 8) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 8", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 8T) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 8T", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 9 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/85.0.4183.127 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 9 Pro", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 9 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 9 Pro", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 9 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.105 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 9 Pro", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 9S) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 9S", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A105FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A105FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A202F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/85.0.4183.127 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A202F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A202F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A202F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A207M) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A207M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A217F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A217F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A405FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/85.0.4183.127 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A405FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A405FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.88 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.88", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A405FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.88"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A405FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A405FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A415F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A415F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A505FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.185 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A505FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A505U) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.185 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.185", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.185"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A515F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A515F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A515F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A515F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A515U) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A515U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A705FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A705FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A705U) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.111 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.111", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A705U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A715F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.90 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A715F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A750FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A750FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G398FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G398FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G780F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G780F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G781B) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G781B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G960F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/85.0.4183.127 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G960F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G960F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G960F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.90 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G960U) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G965F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.198 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G965F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G965F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G965F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G965F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G970F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.198 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G970F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/81.0.4044.122 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "81.0.4044.122", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "81.0.4044.122"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.111 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.111", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.88 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.88", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.88"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G977U) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.193 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.193", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G977U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.193"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G980F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G980F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G981B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-J600FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J600FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-M307FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M307FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-N960F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-N960F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-N960U) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.198 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N960U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-N975F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/85.0.4183.127 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N975F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-N975U) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.198 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N975U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-N986B) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N986B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-T725) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T725", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SNE-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SNE-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SNE-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.90 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.90", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SNE-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.90"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; STK-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "STK-LX1", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; VOG-L29) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.111 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.111", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "VOG-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; VOG-L29) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "VOG-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; VOG-L29) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "VOG-L29", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; WP5) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "WP5"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; YAL-L21) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "YAL-L21", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; ZTE A2020G Pro) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "A2020G Pro", "vendor": "ZTE"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; moto g stylus) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "moto g stylus", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; moto g(7) power) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/85.0.4183.102 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "85.0.4183.102", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "moto g(7) power", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "85.0.4183.102"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; moto g(7) power) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "moto g(7) power", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; moto g(7) power) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "moto g(7) power", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; moto g(8) plus) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "moto g(8) plus", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; moto g(9) plus) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "moto g(9) plus", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; motorola one action) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "one", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; motorola one vision) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "one", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; motorola one) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "one", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 11) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.182 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.182", "major": "88"}, "cpu": {}, "device": {"type": "mobile"}, "engine": {"name": "Blink", "version": "88.0.4324.182"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; CPH2025) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "CPH2025", "vendor": "OPPO"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; GM1903) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "GM1903"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; IN2023) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "IN2023"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; IN2023) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "IN2023"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; LM-G900) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-G900", "vendor": "LG"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; M2002J9G) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "M2002J9G", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Mi 10 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi 10 Pro"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Mi A3) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi A3", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 2 XL) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.111 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.111", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 2 XL", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.111"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 2) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 2", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 3 XL) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3 XL", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 3) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.193 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.193", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.193"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 3) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 3a) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/86.0.4240.198 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "86.0.4240.198", "major": "86"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3a", "vendor": "Google"}, "engine": {"name": "Blink", "version": "86.0.4240.198"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 3a) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.101 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.101", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3a", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.101"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 3a) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 3a", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 4) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/85.0.4183.127 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "85.0.4183.127", "major": "85"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 4", "vendor": "Google"}, "engine": {"name": "Blink", "version": "85.0.4183.127"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 4) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 4", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 4a (5G)) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 4a (5G)"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 4a) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.66", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 4a", "vendor": "Google"}, "engine": {"name": "Blink", "version": "87.0.4280.66"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 4a) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 4a", "vendor": "Google"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 4a) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 4a", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 5", "vendor": "Google"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; RMX1931) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "RMX1931", "vendor": "Realme"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-A515F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A515F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-A715F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A715F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G770F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G770F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G780F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G780F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G781B) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G781B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G970F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G970F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/84.0.4147.125 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "84.0.4147.125", "major": "84"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "84.0.4147.125"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.114", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.114"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.72", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.72"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G977B) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G977B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G980F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G980F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G980F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.105 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G980F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G985F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.141 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.141", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G985F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.141"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G985F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.105 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G985F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G988B) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G988B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G991B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-G7810) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/87.0.4280.67 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "87.0.4280.67", "major": "87"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G7810", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "87.0.4280.67"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-M307FN) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.86 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.86", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-M307FN", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.86"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-N975F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.105 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N975F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-N986B) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-N986B", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-A205U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "90.0.4430.91", "major": "90"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A205U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "90.0.4430.91"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; LM-Q720) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "90.0.4430.91", "major": "90"}, "cpu": {}, "device": {"type": "mobile", "model": "LM-Q720", "vendor": "LG"}, "engine": {"name": "Blink", "version": "90.0.4430.91"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; C21) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "C21"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.1; Android SDK built for x86 Build/NYC) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "55.0.2883.91", "major": "55"}, "cpu": {}, "device": {"type": "mobile", "model": "Android SDK"}, "engine": {"name": "Blink", "version": "55.0.2883.91"}, "os": {"name": "Android", "version": "7.1.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Redmi 7A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "90.0.4430.66", "major": "90"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 7A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "90.0.4430.66"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; CPH1969) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "CPH1969", "vendor": "OPPO"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.1.2; ASUS_T00F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "T00F", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "7.1.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A207M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A207M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-A013M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "81.0.4044.138", "major": "81"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A013M", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "81.0.4044.138"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0; Galaxy Nexus Build/IMM76B) AppleWebKit/537.19 (KHTML, like Gecko) Chrome/68.0.3440.91 Mobile Safari/537.19", "browser": {"name": "Mobile Chrome", "version": "68.0.3440.91", "major": "68"}, "cpu": {}, "device": {"type": "mobile", "model": "Galaxy Nexus", "vendor": "Samsung"}, "engine": {"name": "WebKit", "version": "537.19"}, "os": {"name": "Android", "version": "8.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "91.0.4472.120", "major": "91"}, "cpu": {}, "device": {"type": "mobile"}, "engine": {"name": "Blink", "version": "91.0.4472.120"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 11) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "91.0.4472.120", "major": "91"}, "cpu": {}, "device": {"type": "mobile"}, "engine": {"name": "Blink", "version": "91.0.4472.120"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; TECNO LC8) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.60 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "98.0.4758.60", "major": "98"}, "cpu": {}, "device": {"type": "mobile", "model": "LC8", "vendor": "TECNO"}, "engine": {"name": "Blink", "version": "98.0.4758.60"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G960F) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.105 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G960F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; CPH1803) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.152 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.152", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "CPH1803", "vendor": "OPPO"}, "engine": {"name": "Blink", "version": "88.0.4324.152"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-A025F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.60 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "98.0.4758.60", "major": "98"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A025F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "98.0.4758.60"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; SM-A505U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.89 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "98.0.4758.89", "major": "98"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A505U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "98.0.4758.89"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; vivo 1603 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "58.0.3029.83", "major": "58"}, "cpu": {}, "device": {"type": "mobile", "model": "1603", "vendor": "Vivo"}, "engine": {"name": "Blink", "version": "58.0.3029.83"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SOV37) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.89 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "98.0.4758.89", "major": "98"}, "cpu": {}, "device": {"type": "mobile", "model": "SOV37"}, "engine": {"name": "Blink", "version": "98.0.4758.89"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; SM-G975U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.60 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "98.0.4758.60", "major": "98"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "98.0.4758.60"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; moto g(7) play) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.79 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "100.0.4896.79", "major": "100"}, "cpu": {}, "device": {"type": "mobile", "model": "moto g(7) play", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "100.0.4896.79"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.0.0; ASUS_X00RD) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.40 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "101.0.4951.40", "major": "101"}, "cpu": {}, "device": {"type": "mobile", "model": "X00RD", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "101.0.4951.40"}, "os": {"name": "Android", "version": "8.0.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; RMX3085) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.40 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "101.0.4951.40", "major": "101"}, "cpu": {}, "device": {"type": "mobile", "model": "RMX3085", "vendor": "Realme"}, "engine": {"name": "Blink", "version": "101.0.4951.40"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; M2101K7BG) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.40 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "101.0.4951.40", "major": "101"}, "cpu": {}, "device": {"type": "mobile", "model": "M2101K7BG", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "101.0.4951.40"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "90.0.4430.91", "major": "90"}, "cpu": {}, "device": {"type": "mobile", "model": "Pixel 5", "vendor": "Google"}, "engine": {"name": "Blink", "version": "90.0.4430.91"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "359.0.0.288", "major": "359"}, "cpu": {}, "device": {"type": "mobile", "model": "X00TD", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "359.0.0.288"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; M2007J1SC) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.48 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "101.0.4951.48", "major": "101"}, "cpu": {}, "device": {"type": "mobile", "model": "M2007J1SC", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "101.0.4951.48"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; M2003J15SC) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "107.0.0.0", "major": "107"}, "cpu": {}, "device": {"type": "mobile", "model": "M2003J15SC", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "107.0.0.0"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; vivo 1938) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "91.0.4472.114", "major": "91"}, "cpu": {}, "device": {"type": "mobile", "model": "1938", "vendor": "Vivo"}, "engine": {"name": "Blink", "version": "91.0.4472.114"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; RMX2103) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "107.0.0.0", "major": "107"}, "cpu": {}, "device": {"type": "mobile", "model": "RMX2103", "vendor": "Realme"}, "engine": {"name": "Blink", "version": "107.0.0.0"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; SM-G975U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.60 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "98.0.4758.60", "major": "98"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G975U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "98.0.4758.60"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Primo H8 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "107.0.0.0", "major": "107"}, "cpu": {}, "device": {"type": "mobile", "model": "Primo H8 Pro"}, "engine": {"name": "Blink", "version": "107.0.0.0"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; Redmi Note 9 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "111.0.0.0", "major": "111"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 9 Pro", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "111.0.0.0"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; M2006C3LG) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "111.0.0.0", "major": "111"}, "cpu": {}, "device": {"type": "mobile", "model": "M2006C3LG", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "111.0.0.0"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; CPH2099) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "111.0.0.0", "major": "111"}, "cpu": {}, "device": {"type": "mobile", "model": "CPH2099", "vendor": "OPPO"}, "engine": {"name": "Blink", "version": "111.0.0.0"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; RMX3261) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "105.0.0.0", "major": "105"}, "cpu": {}, "device": {"type": "mobile", "model": "RMX3261", "vendor": "Realme"}, "engine": {"name": "Blink", "version": "105.0.0.0"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 13; SM-A037U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Mobile Safari/537.36  uacq", "browser": {"name": "Mobile Chrome", "version": "112.0.0.0", "major": "112"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A037U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "112.0.0.0"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "115.0.0.0", "major": "115"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "115.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "111.0.0.0", "major": "111"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "111.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 8.1.0; OPPO R11; Build/OPM1.171019.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.4280.141 Mobile Safari/537.36 Firefox-KiToBrowser/115.0", "browser": {"name": "Mobile Chrome", "version": "115.0.4280.141", "major": "115"}, "cpu": {}, "device": {"type": "mobile", "model": "Build/OPM1.171019.011"}, "engine": {"name": "Blink", "version": "115.0.4280.141"}, "os": {"name": "Android", "version": "8.1.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "124.0.0.0", "major": "124"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "124.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.96 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "88.0.4324.96", "major": "88"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G900P", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "88.0.4324.96"}, "os": {"name": "Android", "version": "5.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; CDL-AN50) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "107.0.0.0", "major": "107"}, "cpu": {}, "device": {"type": "mobile", "model": "CDL-AN50", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "107.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; M2006C3MG) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "110.0.0.0", "major": "110"}, "cpu": {}, "device": {"type": "mobile", "model": "M2006C3MG", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "110.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 13; 23054RA19C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "110.0.0.0", "major": "110"}, "cpu": {}, "device": {"type": "mobile", "model": "23054RA19C", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "110.0.0.0"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 13; M2012K11AG) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "110.0.0.0", "major": "110"}, "cpu": {}, "device": {"type": "mobile", "model": "M2012K11AG", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "110.0.0.0"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G973U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 13; SM-A037U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Mobile Safari/537.36  uacq", "browser": {"name": "Mobile Chrome", "version": "112.0.0.0", "major": "112"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A037U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "112.0.0.0"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; CPH2199) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "107.0.0.0", "major": "107"}, "cpu": {}, "device": {"type": "mobile", "model": "CPH2199", "vendor": "OPPO"}, "engine": {"name": "Blink", "version": "107.0.0.0"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 13) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Mobile Safari/537.36  uacq", "browser": {"name": "Mobile Chrome", "version": "111.0.0.0", "major": "111"}, "cpu": {}, "device": {"type": "mobile"}, "engine": {"name": "Blink", "version": "111.0.0.0"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Moto G (4)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4590.2 Mobile Safari/537.36 Chrome-Lighthouse", "browser": {"name": "Mobile Chrome", "version": "94.0.4590.2", "major": "94"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (4)", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "94.0.4590.2"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; Redmi Note 3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "116.0.0.0", "major": "116"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi Note 3", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "116.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "124.0.0.0", "major": "124"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "124.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "128.0.0.0", "major": "128"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "128.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; MI 8 Lite) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "116.0.0.0", "major": "116"}, "cpu": {}, "device": {"type": "mobile", "model": "MI 8 Lite", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "116.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "125.0.0.0", "major": "125"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "125.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.6533.99 Mobile Safari/537.36 (compatible; Google-Safety; +http://www.google.com/bot.html)", "browser": {"name": "Mobile Chrome", "version": "127.0.6533.99", "major": "127"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 5", "vendor": "LG"}, "engine": {"name": "Blink", "version": "127.0.6533.99"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; vivo 1603 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "58.0.3029.83", "major": "58"}, "cpu": {}, "device": {"type": "mobile", "model": "1603", "vendor": "Vivo"}, "engine": {"name": "Blink", "version": "58.0.3029.83"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "359.0.0.288", "major": "359"}, "cpu": {}, "device": {"type": "mobile", "model": "X00TD", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "359.0.0.288"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 14; moto g54 5G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "110.0.0.0", "major": "110"}, "cpu": {}, "device": {"type": "mobile", "model": "moto g54 5G", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "110.0.0.0"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; Android 14; TX6s) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.6308.220 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "127.0.6308.220", "major": "127"}, "cpu": {}, "device": {"type": "mobile", "model": "TX6s"}, "engine": {"name": "Blink", "version": "127.0.6308.220"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.139 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)", "browser": {"name": "Mobile Chrome", "version": "131.0.6778.139", "major": "131"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 5", "vendor": "LG"}, "engine": {"name": "Blink", "version": "131.0.6778.139"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.4; Galaxy Nexus Build/IMM76B) AppleWebKit/537.36 (KHTML, like Gecko; Mediapartners-Google) Chrome/131.0.6778.139 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "131.0.6778.139", "major": "131"}, "cpu": {}, "device": {"type": "mobile", "model": "Galaxy Nexus", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "131.0.6778.139"}, "os": {"name": "Android", "version": "4.0.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 14; Z832 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.127 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "128.0.6613.127", "major": "128"}, "cpu": {}, "device": {"type": "mobile", "model": "Z832"}, "engine": {"name": "Blink", "version": "128.0.6613.127"}, "os": {"name": "Android", "version": "14"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "114.0.0.0", "major": "114"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "114.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; 2203129G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "107.0.0.0", "major": "107"}, "cpu": {}, "device": {"type": "mobile", "model": "2203129G", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "107.0.0.0"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; V1921A; Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.4280.141 Mobile Safari/537.36 Firefox-KiToBrowser/124.0", "browser": {"name": "Mobile Chrome", "version": "124.0.4280.141", "major": "124"}, "cpu": {}, "device": {"type": "mobile", "model": "V1921A", "vendor": "Vivo"}, "engine": {"name": "Blink", "version": "124.0.4280.141"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "120.0.0.0", "major": "120"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "120.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 5.0.2; Mi 4i Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.105 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "49.0.2623.105", "major": "49"}, "cpu": {}, "device": {"type": "mobile", "model": "Mi 4i", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "49.0.2623.105"}, "os": {"name": "Android", "version": "5.0.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "359.0.0.288", "major": "359"}, "cpu": {}, "device": {"type": "mobile", "model": "X00TD", "vendor": "ASUS"}, "engine": {"name": "Blink", "version": "359.0.0.288"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; RMX1931) AppleWebKit/537.36 (KHTML, like Gecko) Brave Chrome/89.0.4389.105 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": "RMX1931", "vendor": "Realme"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Nexus 4 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "34.0.1847.114", "major": "34"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 4", "vendor": "LG"}, "engine": {"name": "Blink", "version": "34.0.1847.114"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; SM-J720F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "105.0.0.0", "major": "105"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-J720F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "105.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; Redmi 7A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "90.0.4430.66", "major": "90"}, "cpu": {}, "device": {"type": "mobile", "model": "Redmi 7A", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "90.0.4430.66"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 7.0; Moto G (4)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4590.2 Mobile Safari/537.36 Chrome-Lighthouse", "browser": {"name": "Mobile Chrome", "version": "94.0.4590.2", "major": "94"}, "cpu": {}, "device": {"type": "mobile", "model": "Moto G (4)", "vendor": "Motorola"}, "engine": {"name": "Blink", "version": "94.0.4590.2"}, "os": {"name": "Android", "version": "7.0"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "125.0.0.0", "major": "125"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "125.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 11; M2006C3LG) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Mobile Safari/537.36 uacq", "browser": {"name": "Mobile Chrome", "version": "111.0.0.0", "major": "111"}, "cpu": {}, "device": {"type": "mobile", "model": "M2006C3LG", "vendor": "<PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "111.0.0.0"}, "os": {"name": "Android", "version": "11"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "137.0.0.0", "major": "137"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "137.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "130.0.0.0", "major": "130"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "130.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "127.0.0.0", "major": "127"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "127.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; SM-T865) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6802.83 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "132.0.6802.83", "major": "132"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T865", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "132.0.6802.83"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 10.2; SM-T875) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6808.57 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "134.0.6808.57", "major": "134"}, "cpu": {}, "device": {"type": "tablet", "model": "SM-T875", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "134.0.6808.57"}, "os": {"name": "Android", "version": "10.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; M40_EEA) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6820.57 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "134.0.6820.57", "major": "134"}, "cpu": {}, "device": {"type": "mobile", "model": "M40_EEA"}, "engine": {"name": "Blink", "version": "134.0.6820.57"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 13; Lenovo TB-X505L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6788.74 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "134.0.6788.74", "major": "134"}, "cpu": {}, "device": {"type": "tablet", "model": "TB-X505L", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "134.0.6788.74"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "131.0.0.0", "major": "131"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "131.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "132.0.0.0", "major": "132"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "132.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "135.0.0.0", "major": "135"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "135.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 13) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.5414.117 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "109.0.5414.117", "major": "109"}, "cpu": {}, "device": {"type": "mobile"}, "engine": {"name": "Blink", "version": "109.0.5414.117"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 9; SM-G973U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "69.0.3497.100", "major": "69"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973U", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "69.0.3497.100"}, "os": {"name": "Android", "version": "9"}}, {"ua": "Mozilla/5.0 (Linux; Android 13; A850) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6786.73 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "134.0.6786.73", "major": "134"}, "cpu": {}, "device": {"type": "mobile", "model": "A850"}, "engine": {"name": "Blink", "version": "134.0.6786.73"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 6.0.1; vivo 1603 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "58.0.3029.83", "major": "58"}, "cpu": {}, "device": {"type": "mobile", "model": "1603", "vendor": "Vivo"}, "engine": {"name": "Blink", "version": "58.0.3029.83"}, "os": {"name": "Android", "version": "6.0.1"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.0.4; Galaxy Nexus Build/IMM76B) AppleWebKit/537.36 (KHTML, like Gecko; Mediapartners-Google) Chrome/134.0.6998.165 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "134.0.6998.165", "major": "134"}, "cpu": {}, "device": {"type": "mobile", "model": "Galaxy Nexus", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "134.0.6998.165"}, "os": {"name": "Android", "version": "4.0.4"}}, {"ua": "Mozilla/5.0 (Linux; Android 4.4.2; Nexus 4 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "34.0.1847.114", "major": "34"}, "cpu": {}, "device": {"type": "mobile", "model": "Nexus 4", "vendor": "LG"}, "engine": {"name": "Blink", "version": "34.0.1847.114"}, "os": {"name": "Android", "version": "4.4.2"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; SM-A217F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.56 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "94.0.4606.56", "major": "94"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-A217F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "94.0.4606.56"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 12; Lenovo TB-X606F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6785.62 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "132.0.6785.62", "major": "132"}, "cpu": {}, "device": {"type": "tablet", "model": "TB-X606F", "vendor": "Lenovo"}, "engine": {"name": "Blink", "version": "132.0.6785.62"}, "os": {"name": "Android", "version": "12"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "125.0.0.0", "major": "125"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "125.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "136.0.0.0", "major": "136"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "136.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 13; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6820.72 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "132.0.6820.72", "major": "132"}, "cpu": {}, "device": {"type": "mobile", "model": "SM-G973F", "vendor": "Samsung"}, "engine": {"name": "Blink", "version": "132.0.6820.72"}, "os": {"name": "Android", "version": "13"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; HUAWEI P30 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "89.0.4389.105", "major": "89"}, "cpu": {}, "device": {"type": "mobile", "model": " P30 Pro", "vendor": "<PERSON><PERSON><PERSON>"}, "engine": {"name": "Blink", "version": "89.0.4389.105"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; CPH2179) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.66 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "98.0.4758.66", "major": "98"}, "cpu": {}, "device": {"type": "mobile", "model": "CPH2179", "vendor": "OPPO"}, "engine": {"name": "Blink", "version": "98.0.4758.66"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "120.0.0.0", "major": "120"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "120.0.0.0"}, "os": {"name": "Android", "version": "10"}}, {"ua": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Mobile Safari/537.36", "browser": {"name": "Mobile Chrome", "version": "124.0.0.0", "major": "124"}, "cpu": {}, "device": {"type": "mobile", "model": "K"}, "engine": {"name": "Blink", "version": "124.0.0.0"}, "os": {"name": "Android", "version": "10"}}]