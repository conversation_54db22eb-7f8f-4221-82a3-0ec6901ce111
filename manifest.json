{"manifest_version": 3, "name": "__MSG_extensionName__", "version": "*******", "default_locale": "en", "description": "__MSG_extensionDescription__", "permissions": ["storage", "contextMenus", "scripting", "declarativeNetRequestWithHostAccess"], "host_permissions": ["<all_urls>"], "icons": {"16": "data/icons/active/16.png", "32": "data/icons/active/32.png", "48": "data/icons/active/48.png", "64": "data/icons/active/64.png", "128": "data/icons/active/128.png", "256": "data/icons/active/256.png"}, "storage": {"managed_schema": "schema.json"}, "background": {"service_worker": "worker.js", "scripts": ["context.js", "external/ua-parser.min.js", "agent.js", "network.js", "managed.js", "worker.js"]}, "action": {"default_popup": "data/popup/index.html", "default_icon": {"16": "data/icons/ignored/16.png", "32": "data/icons/ignored/32.png", "48": "data/icons/ignored/48.png"}}, "homepage_url": "https://webextension.org/listing/useragent-switcher.html", "options_ui": {"page": "data/options/index.html", "open_in_tab": true}, "commands": {"_execute_action": {"description": "Execute Action"}}, "browser_specific_settings": {"gecko": {"id": "{a6c4a591-f1b2-4f03-b3ff-767e5bedf4e7}", "strict_min_version": "132.0"}}}