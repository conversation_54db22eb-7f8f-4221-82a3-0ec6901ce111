[{"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/69.0.3497.81 Safari/537.36 SQWatcher/201906 (sqcompliance.com/sqwatcher.html)", "browser": {"name": "Chrome Headless", "version": "69.0.3497.81", "major": "69"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "69.0.3497.81"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/75.0.3770.142 Safari/537.36", "browser": {"name": "Chrome Headless", "version": "75.0.3770.142", "major": "75"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "75.0.3770.142"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/105.0.5195.125 Safari/537.36", "browser": {"name": "Chrome Headless", "version": "105.0.5195.125", "major": "105"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "105.0.5195.125"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/120.0.6099.28 Safari/537.36", "browser": {"name": "Chrome Headless", "version": "120.0.6099.28", "major": "120"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "120.0.6099.28"}, "os": {"name": "Linux", "version": "x86_64"}}, {"ua": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/96.0.4664.45 Safari/537.36", "browser": {"name": "Chrome Headless", "version": "96.0.4664.45", "major": "96"}, "cpu": {"architecture": "amd64"}, "device": {}, "engine": {"name": "Blink", "version": "96.0.4664.45"}, "os": {"name": "Linux", "version": "x86_64"}}]